/**
 * Inicializador de Autenticação EXCLUSIVAMENTE HttpOnly
 * Componente de loading e configuração para cookies HttpOnly
 */

import React, { useEffect, useState } from 'react';
import { Spin, Alert, Button, Card, Space, Typography, Progress } from 'antd';
import { 
  LoadingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined,
  LockOutlined,
  ShieldCheckOutlined
} from '@ant-design/icons';
import { usePureHttpOnlyAuth } from '../../contexts/PureHttpOnlyAuthProvider';
import { logger } from '../../utils/logger';

const { Title, Text, Paragraph } = Typography;

/**
 * Estados de inicialização
 */
const INIT_STATES = {
  INITIALIZING: 'initializing',
  SUCCESS: 'success',
  ERROR: 'error',
  RETRY: 'retry'
};

/**
 * Componente de loading durante inicialização HttpOnly
 */
const HttpOnlyInitializationLoader = ({ progress, message, details }) => (
  <div style={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '24px',
    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
  }}>
    <Card 
      style={{ 
        textAlign: 'center',
        maxWidth: '500px',
        width: '100%',
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0,0,0,0.1)',
        border: '2px solid #00B050'
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ position: 'relative' }}>
          <Spin 
            indicator={<LoadingOutlined style={{ fontSize: 48, color: '#00B050' }} />}
            size="large"
          />
          <LockOutlined 
            style={{ 
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: 20,
              color: '#fff',
              backgroundColor: '#00B050',
              borderRadius: '50%',
              padding: '4px'
            }}
          />
        </div>
        
        <Title level={3} style={{ margin: 0, color: '#333' }}>
          <ShieldCheckOutlined style={{ color: '#00B050', marginRight: '8px' }} />
          Inicializando Sistema Seguro
        </Title>
        
        <Alert
          message="Modo HttpOnly Exclusivo"
          description="Sistema configurado para máxima segurança com cookies HttpOnly. Nenhum token será armazenado no navegador."
          type="success"
          showIcon
          style={{ textAlign: 'left' }}
        />
        
        <Progress 
          percent={progress} 
          strokeColor="#00B050"
          showInfo={false}
          style={{ margin: '16px 0' }}
        />
        
        <Text type="secondary" style={{ fontSize: '16px' }}>
          {message}
        </Text>
        
        {details && (
          <div style={{ textAlign: 'left', width: '100%' }}>
            <Text strong>Configurações de Segurança:</Text>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {details.map((detail, index) => (
                <li key={index}>
                  <Text type="secondary" style={{ fontSize: '14px' }}>
                    {detail}
                  </Text>
                </li>
              ))}
            </ul>
          </div>
        )}
      </Space>
    </Card>
  </div>
);

/**
 * Componente de erro de inicialização HttpOnly
 */
const HttpOnlyInitializationError = ({ error, onRetry, details }) => (
  <div style={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '24px',
    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
  }}>
    <Card 
      style={{ 
        textAlign: 'center',
        maxWidth: '600px',
        width: '100%',
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0,0,0,0.1)',
        border: '2px solid #ff4d4f'
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <ExclamationCircleOutlined 
          style={{ fontSize: 48, color: '#ff4d4f' }}
        />
        
        <Title level={3} style={{ margin: 0, color: '#333' }}>
          Erro na Inicialização HttpOnly
        </Title>
        
        <Alert
          message="Falha na Configuração Segura"
          description={error}
          type="error"
          showIcon
          style={{ textAlign: 'left' }}
        />
        
        {details && details.length > 0 && (
          <Alert
            message="Detalhes Técnicos"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {details.map((detail, index) => (
                  <li key={index}>{detail}</li>
                ))}
              </ul>
            }
            type="info"
            showIcon
            style={{ textAlign: 'left' }}
          />
        )}
        
        <Alert
          message="Verificações de Segurança"
          description={
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li>✅ Cookies HttpOnly habilitados</li>
              <li>✅ Bearer tokens desabilitados</li>
              <li>✅ localStorage limpo</li>
              <li>❌ Conexão com backend falhando</li>
            </ul>
          }
          type="warning"
          showIcon
          style={{ textAlign: 'left' }}
        />
        
        <Space>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={onRetry}
            style={{ backgroundColor: '#00B050', borderColor: '#00B050' }}
          >
            Tentar Novamente
          </Button>
          
          <Button 
            onClick={() => window.location.reload()}
          >
            Recarregar Página
          </Button>
        </Space>
        
        {process.env.NODE_ENV === 'development' && (
          <Alert
            message="Modo Desenvolvimento"
            description="Verifique se o backend está rodando e se os endpoints /auth/* estão disponíveis."
            type="warning"
            showIcon
            style={{ textAlign: 'left' }}
          />
        )}
      </Space>
    </Card>
  </div>
);

/**
 * Componente principal de inicialização HttpOnly
 */
export const HttpOnlyAuthInitializer = ({ children, fallback = null }) => {
  const auth = usePureHttpOnlyAuth();
  const [initState, setInitState] = useState(INIT_STATES.INITIALIZING);
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Configurando segurança...');
  const [details, setDetails] = useState([]);
  const [error, setError] = useState(null);

  /**
   * Processo de inicialização HttpOnly
   */
  const initialize = async () => {
    try {
      setInitState(INIT_STATES.INITIALIZING);
      setProgress(0);
      setError(null);
      setDetails([]);

      // Etapa 1: Configuração de segurança
      setMessage('Configurando cookies HttpOnly...');
      setDetails([
        '🔒 Forçando modo HttpOnly exclusivo',
        '🧹 Limpando tokens do localStorage',
        '🚫 Desabilitando Bearer tokens'
      ]);
      setProgress(20);
      
      await new Promise(resolve => setTimeout(resolve, 800));

      // Etapa 2: Configuração do Axios
      setMessage('Configurando interceptors seguros...');
      setDetails([
        '🔧 Configurando withCredentials=true',
        '❌ Removendo headers Authorization',
        '🍪 Habilitando cookies automáticos'
      ]);
      setProgress(40);
      
      await new Promise(resolve => setTimeout(resolve, 800));

      // Etapa 3: Verificação de conectividade
      setMessage('Verificando conectividade com backend...');
      setDetails([
        '🌐 Testando endpoint /auth/verify',
        '🔍 Verificando cookies HttpOnly',
        '⚡ Validando interceptors'
      ]);
      setProgress(60);
      
      await new Promise(resolve => setTimeout(resolve, 800));

      // Etapa 4: Verificação de autenticação
      setMessage('Verificando autenticação existente...');
      setDetails([
        '👤 Verificando sessão via cookies',
        '🔐 Validando permissões',
        '⏰ Configurando monitoramento'
      ]);
      setProgress(80);
      
      await new Promise(resolve => setTimeout(resolve, 800));

      // Etapa 5: Finalização
      setMessage('Sistema seguro inicializado!');
      setDetails([
        '✅ Autenticação HttpOnly ativa',
        '🛡️ Máxima segurança configurada',
        '🚀 Sistema pronto para uso'
      ]);
      setProgress(100);
      
      await new Promise(resolve => setTimeout(resolve, 500));

      setInitState(INIT_STATES.SUCCESS);
      
      logger.info('🔒 Inicialização HttpOnly concluída com sucesso', {
        isInitialized: auth.isInitialized,
        authMethod: 'httponly',
        bearerTokens: false,
        localStorage: false,
        httpOnlyOnly: true
      });

    } catch (error) {
      logger.error('❌ Erro na inicialização HttpOnly', { error: error.message });
      setError(error.message);
      setInitState(INIT_STATES.ERROR);
      
      // Adicionar detalhes do erro
      const errorDetails = [
        `Erro: ${error.message}`,
        `Ambiente: ${process.env.NODE_ENV}`,
        `Stage: ${process.env.REACT_APP_STAGE || 'Não definido'}`,
        `API URL: ${process.env.REACT_APP_API_PERMISSION || 'Não definido'}`,
        'Modo: HttpOnly exclusivo',
        'Bearer tokens: Desabilitados'
      ];
      setDetails(errorDetails);
    }
  };

  /**
   * Retry da inicialização
   */
  const handleRetry = () => {
    setInitState(INIT_STATES.RETRY);
    setTimeout(initialize, 1000);
  };

  /**
   * Inicializar na montagem
   */
  useEffect(() => {
    // Aguardar um pouco para garantir que o contexto está pronto
    const timer = setTimeout(initialize, 100);
    return () => clearTimeout(timer);
  }, []);

  // Renderizar baseado no estado
  switch (initState) {
    case INIT_STATES.INITIALIZING:
    case INIT_STATES.RETRY:
      return (
        <HttpOnlyInitializationLoader 
          progress={progress}
          message={message}
          details={details}
        />
      );

    case INIT_STATES.ERROR:
      return (
        <HttpOnlyInitializationError 
          error={error}
          onRetry={handleRetry}
          details={details}
        />
      );

    case INIT_STATES.SUCCESS:
      // Se ainda está carregando a autenticação, mostrar loading
      if (auth.isLoading) {
        return (
          <HttpOnlyInitializationLoader 
            progress={90}
            message="Verificando autenticação via cookies HttpOnly..."
            details={['🍪 Carregando dados do usuário via cookies seguros']}
          />
        );
      }
      
      // Sistema inicializado com sucesso
      return children;

    default:
      return fallback || children;
  }
};

/**
 * HOC para envolver componentes com inicialização HttpOnly
 */
export const withHttpOnlyAuthInitializer = (Component) => {
  return (props) => (
    <HttpOnlyAuthInitializer>
      <Component {...props} />
    </HttpOnlyAuthInitializer>
  );
};

export default HttpOnlyAuthInitializer;
