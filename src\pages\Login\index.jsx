import React, { useEffect } from "react";

import { Row, Col, Image, Space, Divider } from "antd";
import { Navigate } from "react-router-dom";
import Logo from "../../assets/images/logo_full.png";

export const Login = () => {
  useEffect(() => {
    window.location.replace(
      process.env.NODE_ENV === "development"
        ? "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?client_id=3no5aulnqvut73n2hq93tqdrq0&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+phone+profile&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fmfa"
        : process.env.REACT_APP_STAGE === "dev"
        ? "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?identity_provider=AzureAD&redirect_uri=https://dev.dsm.darede.com.br/mfa&response_type=CODE&client_id=a612mhbbrvrh45ec6n8amqf5i&scope=aws.cognito.signin.user.admin%20email%20openid%20phone%20profile"
        : process.env.REACT_APP_STAGE === "hml"
        ? "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?identity_provider=AzureAD&redirect_uri=https://hml.dsm.darede.com.br/mfa&response_type=CODE&client_id=74k0l615eeerd40t2rb8tthffb&scope=aws.cognito.signin.user.admin%20email%20openid%20phone%20profile"
        : "https://dsm-application.auth.us-east-1.amazoncognito.com/oauth2/authorize?client_id=56abme2ar7cabfnep9919ijbed&response_type=code&scope=email+openid+phone&redirect_uri=https%3A%2F%2Fdsm.darede.com.br%2Fmfa"
    );
  }, []);

  if (
    ["", null, undefined].includes(localStorage.getItem("@dsm/name")) === false
  ) {
    return <Navigate to={"/"} />;
  }
  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider style={{ padding: "10px" }}>
              Você será direcionado para a página de login,
              <br /> utilize o seu usuário de rede.
            </Divider>
          </Space>
        </Col>
      </Row>
    </>
  );
};
