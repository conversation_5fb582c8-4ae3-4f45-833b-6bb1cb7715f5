<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste SideMenu DSM - Hover Behavior</title>
    <link href="https://fonts.googleapis.com/css2?family=Libre+Franklin:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Libre Franklin', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            margin: 0;
            background-color: #f5f5f5;
            display: flex;
        }
        
        /* Simulação do SideMenu */
        .sidemenu-container {
            width: 245px;
            height: 100vh;
            background-color: rgb(53, 53, 53);
            color: white;
            padding: 20px 0;
        }
        
        .menu-group-title {
            color: rgba(255, 255, 255, 0.67);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 8px 24px;
            margin: 16px 0 8px 0;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: rgba(255, 255, 255, 0.85);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
            height: 40px;
            line-height: 40px;
        }
        
        .menu-item-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        /* Estado normal */
        .menu-item:not(.selected) {
            background-color: transparent;
            color: rgba(255, 255, 255, 0.85);
        }
        
        /* Estado hover - COMPORTAMENTO ESPERADO */
        .menu-item:not(.selected):hover {
            background-color: rgba(0, 176, 80, 0.2) !important;
            color: #00B050 !important;
        }
        
        /* Estado selecionado */
        .menu-item.selected {
            background-color: #00B050 !important;
            color: #ffffff !important;
        }
        
        .menu-item.selected:hover {
            background-color: #00B050 !important;
            color: #ffffff !important;
        }
        
        /* Conteúdo principal */
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .test-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: #52c41a; }
        .status-error { background-color: #ff4d4f; }
        .status-warning { background-color: #faad14; }
        
        .color-sample {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        h1 { color: #00B050; }
        h2 { color: #404040; }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #f0f0f0;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background-color: #fafafa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="sidemenu-container">
        <div class="menu-group-title">Dashboard</div>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">🏠</span>
            Home
        </a>
        <a href="#" class="menu-item selected">
            <span class="menu-item-icon">📊</span>
            Cockpit - SDM
        </a>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">📈</span>
            Cockpit - PDM
        </a>
        
        <div class="menu-group-title">Administração</div>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">👥</span>
            Clientes
        </a>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">📋</span>
            Contratos
        </a>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">💰</span>
            Gerenciamento de Custos
        </a>
        
        <div class="menu-group-title">Operações</div>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">🎫</span>
            Tickets
        </a>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">📁</span>
            Arquivos
        </a>
        <a href="#" class="menu-item">
            <span class="menu-item-icon">🔐</span>
            Conjuntos de Permissões
        </a>
    </div>
    
    <div class="main-content">
        <h1>🔍 Teste de Hover do SideMenu DSM</h1>
        
        <div class="test-panel">
            <h2>Status do Comportamento do Hover</h2>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Estado</th>
                        <th>Cor de Fundo</th>
                        <th>Cor do Texto</th>
                        <th>Comportamento Esperado</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Item Normal</strong></td>
                        <td>
                            <span class="color-sample" style="background-color: rgb(53, 53, 53);"></span>
                            rgb(53, 53, 53)
                        </td>
                        <td>
                            <span class="color-sample" style="background-color: rgba(255, 255, 255, 0.85);"></span>
                            rgba(255, 255, 255, 0.85)
                        </td>
                        <td>Fundo escuro, texto branco</td>
                        <td><span class="status-indicator status-ok"></span>OK</td>
                    </tr>
                    <tr>
                        <td><strong>Item Hover</strong></td>
                        <td>
                            <span class="color-sample" style="background-color: rgba(0, 176, 80, 0.2);"></span>
                            rgba(0, 176, 80, 0.2)
                        </td>
                        <td>
                            <span class="color-sample" style="background-color: #00B050;"></span>
                            #00B050
                        </td>
                        <td>Fundo verde claro, texto verde</td>
                        <td><span class="status-indicator status-warning"></span>TESTAR</td>
                    </tr>
                    <tr>
                        <td><strong>Item Selecionado</strong></td>
                        <td>
                            <span class="color-sample" style="background-color: #00B050;"></span>
                            #00B050
                        </td>
                        <td>
                            <span class="color-sample" style="background-color: #ffffff;"></span>
                            #ffffff
                        </td>
                        <td>Fundo verde, texto branco</td>
                        <td><span class="status-indicator status-ok"></span>OK</td>
                    </tr>
                    <tr>
                        <td><strong>Item Selecionado Hover</strong></td>
                        <td>
                            <span class="color-sample" style="background-color: #00B050;"></span>
                            #00B050
                        </td>
                        <td>
                            <span class="color-sample" style="background-color: #ffffff;"></span>
                            #ffffff
                        </td>
                        <td>Mantém fundo verde, texto branco</td>
                        <td><span class="status-indicator status-ok"></span>OK</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-panel">
            <h2>🔧 Correções Implementadas</h2>
            <ul>
                <li>✅ <strong>ConfigProvider Menu:</strong> Removido para evitar conflitos</li>
                <li>✅ <strong>antd-custom.css:</strong> Regras genéricas de menu removidas</li>
                <li>✅ <strong>layout-fixes.css:</strong> Exceções para menu dark adicionadas</li>
                <li>✅ <strong>Transição global:</strong> Removida para evitar interferência</li>
                <li>✅ <strong>SideMenu.css:</strong> Especificidade aumentada</li>
                <li>✅ <strong>Seletores:</strong> Adicionados .ant-layout-sider para maior prioridade</li>
            </ul>
        </div>
        
        <div class="test-panel">
            <h2>📋 Instruções de Teste</h2>
            <ol>
                <li><strong>Passe o mouse</strong> sobre os itens do menu à esquerda</li>
                <li><strong>Verifique</strong> se o fundo fica verde claro (rgba(0, 176, 80, 0.2))</li>
                <li><strong>Confirme</strong> se o texto fica verde (#00B050)</li>
                <li><strong>Teste</strong> o item selecionado (Cockpit - SDM)</li>
                <li><strong>Valide</strong> se o item selecionado mantém cores no hover</li>
            </ol>
        </div>
        
        <div class="test-panel">
            <h2>🎯 Resultado Esperado</h2>
            <p><strong>Comportamento idêntico ao frontend original:</strong></p>
            <ul>
                <li>Hover suave com transição de 0.3s</li>
                <li>Fundo verde claro transparente no hover</li>
                <li>Texto verde DSM no hover</li>
                <li>Item selecionado mantém cores no hover</li>
                <li>Ícones seguem a cor do texto</li>
            </ul>
        </div>
        
        <footer style="text-align: center; margin-top: 40px; color: #666; border-top: 1px solid #f0f0f0; padding-top: 20px;">
            <p>🔧 <strong>Se o hover não estiver funcionando corretamente, verifique:</strong></p>
            <p>1. Console do navegador para erros CSS</p>
            <p>2. DevTools para verificar quais regras estão sendo aplicadas</p>
            <p>3. Ordem de importação dos arquivos CSS</p>
            <p>4. Especificidade dos seletores CSS</p>
        </footer>
    </div>
</body>
</html>
