/**
 * Utilitários para otimização de performance
 */
import React from "react";

// Cache simples para requisições
const requestCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Cache para requisições com TTL
 */
export const cacheRequest = (key, data) => {
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  });
};

/**
 * Recuperar dados do cache se ainda válidos
 */
export const getCachedRequest = (key) => {
  const cached = requestCache.get(key);
  if (!cached) return null;
  
  const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
  if (isExpired) {
    requestCache.delete(key);
    return null;
  }
  
  return cached.data;
};

/**
 * Debounce para funções
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle para funções
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Otimizar handlers de eventos
 */
export const optimizeEventHandlers = () => {
  // Otimizar scroll events
  let scrollTimeout;
  const originalAddEventListener = window.addEventListener;
  
  window.addEventListener = function(type, listener, options) {
    if (type === 'scroll') {
      const throttledListener = throttle(listener, 16); // 60fps
      return originalAddEventListener.call(this, type, throttledListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };
};

/**
 * Limpar cache periodicamente
 */
export const startCacheCleanup = () => {
  setInterval(() => {
    const now = Date.now();
    for (const [key, value] of requestCache.entries()) {
      if (now - value.timestamp > CACHE_DURATION) {
        requestCache.delete(key);
      }
    }
  }, CACHE_DURATION);
};

/**
 * Otimizar re-renders desnecessários
 */
export const memoizeComponent = (Component) => {
  return React.memo(Component, (prevProps, nextProps) => {
    // Comparação shallow das props
    const prevKeys = Object.keys(prevProps);
    const nextKeys = Object.keys(nextProps);
    
    if (prevKeys.length !== nextKeys.length) {
      return false;
    }
    
    for (let key of prevKeys) {
      if (prevProps[key] !== nextProps[key]) {
        return false;
      }
    }
    
    return true;
  });
};

/**
 * Lazy loading para imagens
 */
export const lazyLoadImages = () => {
  const images = document.querySelectorAll('img[data-src]');
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
        observer.unobserve(img);
      }
    });
  });
  
  images.forEach(img => imageObserver.observe(img));
};

/**
 * Inicializar todas as otimizações
 */
export const initializePerformanceOptimizations = () => {
  optimizeEventHandlers();
  startCacheCleanup();
  lazyLoadImages();
  
};

export default {
  cacheRequest,
  getCachedRequest,
  debounce,
  throttle,
  optimizeEventHandlers,
  startCacheCleanup,
  memoizeComponent,
  lazyLoadImages,
  initializePerformanceOptimizations
};
