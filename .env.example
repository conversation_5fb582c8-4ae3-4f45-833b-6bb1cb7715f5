# Environment Configuration
NODE_ENV=development
REACT_APP_STAGE=dev

# Core API Endpoints - Environment Specific
# Local Development
# REACT_APP_API_BASE_URL=http://localhost:8000/
# REACT_APP_API_PERMISSION=http://localhost:8000/

# Development
REACT_APP_API_BASE_URL=https://api.dev.dsm.com.br/
REACT_APP_API_PERMISSION=https://api.dev.dsm.com.br/
REACT_APP_API_PROPOSALS=https://proposals-api.dev.dsm.com.br/
REACT_APP_API_REPORTS_URL=https://reports-api.dev.dsm.com.br/

# Homologation (change dev to hml for HML environment)
# REACT_APP_API_BASE_URL=https://api.hml.dsm.com.br/
# REACT_APP_API_PERMISSION=https://api.hml.dsm.com.br/

# Production (change dev to www or remove subdomain for PROD)
# REACT_APP_API_BASE_URL=https://api.dsm.com.br/
# REACT_APP_API_PERMISSION=https://api.dsm.com.br/

# External Service APIs
REACT_APP_API_OTRS_BASE_URL=https://otrs-api.dev.dsm.com.br/
REACT_APP_API_CUSTOMER_PORTAL=https://customer-portal.dev.dsm.com.br/
REACT_APP_TICKET_API=https://ticket-api.dev.dsm.com.br/
REACT_APP_PIPEDRIVE_API=https://pipedrive-api.dev.dsm.com.br/
REACT_APP_CRM_API=https://crm-api.dev.dsm.com.br/

# AWS Configuration
REACT_APP_AWS_ACCOUNT_ID=************
REACT_APP_AWS_SECRET_ID=your-secret-id
REACT_APP_USER_POOL_ID=us-east-1_XXXXXXXXX

# Cognito & Authentication
REACT_APP_COGNITO_PARSE=https://api.dev.dsm.com.br/cognito/parse

# AWS Cognito Configuration (Required for new authentication system)
REACT_APP_COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX
REACT_APP_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
REACT_APP_COGNITO_DOMAIN=dsm-auth-dev

# External Integrations
REACT_APP_GOOGLE_KEY=your-google-api-key
REACT_APP_MS_TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/your-webhook-url

# Billing & Contract Links
REACT_APP_BILLING_LINK=https://billing.dev.dsm.com.br/
REACT_APP_BILLING_CONTRACT_LINK=https://billing-contract.dev.dsm.com.br/

# Policy Management
REACT_APP_API_ATTACH_POLICY=https://policy-api.dev.dsm.com.br/

# Build & Performance Configuration
GENERATE_SOURCEMAP=false
REACT_APP_VERSION=$npm_package_version

# Security Configuration
REACT_APP_ENABLE_NEW_AUTH=true
REACT_APP_CSP_NONCE=

# Feature Flags
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_ENABLE_ERROR_BOUNDARY=true
REACT_APP_ENABLE_ANALYTICS=false