/**
 * Configuração do AWS Amplify para Cognito
 * Configuração otimizada para cookies HttpOnly
 */

import { Amplify } from 'aws-amplify';
import { logger } from '../utils/logger';

// Configuração do Cognito
const amplifyConfig = {
  Auth: {
    // Região da AWS
    region: 'us-east-1',
    
    // User Pool ID
    userPoolId: 'us-east-1_VCf8aHRIZ',
    
    // App Client ID baseado no ambiente
    userPoolWebClientId: process.env.NODE_ENV === 'development' 
      ? '3no5aulnqvut73n2hq93tqdrq0'  // Development
      : process.env.REACT_APP_STAGE === 'dev'
      ? 'a612mhbbrvrh45ec6n8amqf5i'   // Dev
      : process.env.REACT_APP_STAGE === 'hml'
      ? '74k0l615eeerd40t2rb8tthffb'   // Homologação
      : '56abme2ar7cabfnep9919ijbed',  // Produção
    
    // Configurações OAuth
    oauth: {
      domain: 'dsm-application.auth.us-east-1.amazoncognito.com',
      scope: ['email', 'openid', 'phone', 'profile', 'aws.cognito.signin.user.admin'],
      
      // URLs de redirecionamento baseadas no ambiente
      redirectSignIn: process.env.NODE_ENV === 'development'
        ? 'http://localhost:3000/mfa'
        : process.env.REACT_APP_STAGE === 'dev'
        ? 'https://dev.dsm.darede.com.br/mfa'
        : process.env.REACT_APP_STAGE === 'hml'
        ? 'https://hml.dsm.darede.com.br/mfa'
        : 'https://dsm.darede.com.br/mfa',
      
      redirectSignOut: process.env.NODE_ENV === 'development'
        ? 'http://localhost:3000/login'
        : process.env.REACT_APP_STAGE === 'dev'
        ? 'https://dev.dsm.darede.com.br/login'
        : process.env.REACT_APP_STAGE === 'hml'
        ? 'https://hml.dsm.darede.com.br/login'
        : 'https://dsm.darede.com.br/login',
      
      responseType: 'code',
      
      // Configurações específicas por ambiente
      ...(process.env.NODE_ENV === 'development' 
        ? {
            // Desenvolvimento - login direto
          }
        : {
            // Produção - usar Azure AD
            identityProvider: 'AzureAD'
          }
      )
    },
    
    // Configurações de cookies
    cookieStorage: {
      domain: process.env.NODE_ENV === 'development' 
        ? 'localhost' 
        : process.env.REACT_APP_STAGE === 'dev'
        ? '.dsm.darede.com.br'
        : process.env.REACT_APP_STAGE === 'hml'
        ? '.dsm.darede.com.br'
        : '.dsm.darede.com.br',
      path: '/',
      expires: 365,
      sameSite: 'lax',
      secure: process.env.NODE_ENV !== 'development'
    },
    
    // Configurações de autenticação
    authenticationFlowType: 'USER_SRP_AUTH',
    
    // Configurações de storage
    storage: typeof window !== 'undefined' ? window.localStorage : null,
    
    // Configurações de rede
    mandatorySignIn: false,
    
    // Configurações de refresh
    refreshHandlers: {
      'custom': async () => {
        // Handler customizado para refresh de tokens
        logger.debug('Amplify: Executando refresh customizado de tokens');
      }
    }
  },
  
  // Configurações de API (se necessário)
  API: {
    endpoints: [
      {
        name: 'dsm-api',
        endpoint: process.env.REACT_APP_API_PERMISSION,
        region: 'us-east-1'
      }
    ]
  },
  
  // Configurações de Storage (se necessário)
  Storage: {
    AWSS3: {
      bucket: `${process.env.REACT_APP_STAGE}-dsm-storage`,
      region: 'us-east-1'
    }
  }
};

/**
 * Inicializar Amplify
 */
export const initializeAmplify = () => {
  try {
    Amplify.configure(amplifyConfig);
    
    logger.info('AWS Amplify configurado com sucesso', {
      userPoolId: amplifyConfig.Auth.userPoolId,
      clientId: amplifyConfig.Auth.userPoolWebClientId,
      environment: process.env.NODE_ENV,
      stage: process.env.REACT_APP_STAGE
    });
    
    return true;
  } catch (error) {
    logger.error('Erro ao configurar AWS Amplify', {
      error: error.message,
      config: amplifyConfig
    });
    return false;
  }
};

/**
 * Obter configuração atual do Amplify
 */
export const getAmplifyConfig = () => {
  return amplifyConfig;
};

/**
 * Obter URL de login OAuth
 */
export const getOAuthLoginUrl = () => {
  const config = amplifyConfig.Auth.oauth;
  const params = new URLSearchParams({
    client_id: amplifyConfig.Auth.userPoolWebClientId,
    response_type: config.responseType,
    scope: config.scope.join(' '),
    redirect_uri: config.redirectSignIn,
    ...(config.identityProvider && { identity_provider: config.identityProvider })
  });
  
  return `https://${config.domain}/oauth2/authorize?${params.toString()}`;
};

/**
 * Obter URL de logout OAuth
 */
export const getOAuthLogoutUrl = () => {
  const config = amplifyConfig.Auth.oauth;
  const params = new URLSearchParams({
    client_id: amplifyConfig.Auth.userPoolWebClientId,
    logout_uri: config.redirectSignOut
  });
  
  return `https://${config.domain}/logout?${params.toString()}`;
};

/**
 * Validar configuração do Amplify
 */
export const validateAmplifyConfig = () => {
  const errors = [];
  
  if (!amplifyConfig.Auth.userPoolId) {
    errors.push('User Pool ID não configurado');
  }
  
  if (!amplifyConfig.Auth.userPoolWebClientId) {
    errors.push('App Client ID não configurado');
  }
  
  if (!amplifyConfig.Auth.oauth.domain) {
    errors.push('Domínio OAuth não configurado');
  }
  
  if (!amplifyConfig.Auth.oauth.redirectSignIn) {
    errors.push('URL de redirecionamento não configurada');
  }
  
  if (errors.length > 0) {
    logger.error('Configuração do Amplify inválida', { errors });
    return { valid: false, errors };
  }
  
  logger.info('Configuração do Amplify válida');
  return { valid: true, errors: [] };
};

/**
 * Configurações de debug para desenvolvimento
 */
if (process.env.NODE_ENV === 'development') {
  // Habilitar logs detalhados do Amplify
  Amplify.Logger.LOG_LEVEL = 'DEBUG';
  
  // Log da configuração (sem dados sensíveis)
  logger.debug('Configuração do Amplify (desenvolvimento)', {
    userPoolId: amplifyConfig.Auth.userPoolId,
    region: amplifyConfig.Auth.region,
    domain: amplifyConfig.Auth.oauth.domain,
    redirectSignIn: amplifyConfig.Auth.oauth.redirectSignIn,
    environment: process.env.NODE_ENV,
    stage: process.env.REACT_APP_STAGE
  });
}

export default amplifyConfig;
