/**
 * Utilitário para criar registro de permissões padrão
 * Resolve o problema do Permission ID "1" não encontrado
 */

import { dynamoPost } from '../service/apiDsmDynamo';

/**
 * Estrutura completa de permissões padrão para desenvolvimento
 */
export const DEFAULT_PERMISSION_RECORD = {
  id: "1",
  name_permission: "Permissão Padrão de Desenvolvimento",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  permissions: [
    // Home / Dashboard
    {
      page: "Home",
      actions: [
        { code: "view_dashboard" },
        { code: "view_statistics" },
        { code: "view_charts" },
        { code: "view_summary" }
      ]
    },
    
    // Catálogo de Serviços
    {
      page: "Catálogo de Serviços",
      actions: [
        { code: "view_name" },
        { code: "view_description" },
        { code: "view_tag" },
        { code: "view_edit" },
        { code: "view_actions" },
        { code: "create_service" },
        { code: "edit_service" },
        { code: "delete_service" },
        { code: "view_service_details" },
        { code: "export_services" },
        { code: "create_service_management" },
        { code: "view_services" },
        { code: "manage_services" },
        { code: "toggle_service_status" },
        { code: "view_catalog" },
        { code: "manage_catalog" },
        { code: "admin_services" }
      ]
    },
    
    // Clientes
    {
      page: "Clientes",
      actions: [
        { code: "view_clients" },
        { code: "create_client" },
        { code: "edit_client" },
        { code: "delete_client" },
        { code: "view_client_details" },
        { code: "view_client_contracts" },
        { code: "view_client_accounts" }
      ]
    },
    
    // Contratos
    {
      page: "Contratos",
      actions: [
        { code: "view_contracts" },
        { code: "create_contract" },
        { code: "edit_contract" },
        { code: "delete_contract" },
        { code: "view_contract_details" },
        { code: "view_contract_info" },
        { code: "view_contract_executives" },
        { code: "view_contract_actions" },
        // Permissões específicas para modal de informações
        { code: "info_modal_view_start_date" },
        { code: "info_modal_view_end_date" },
        { code: "info_modal_view_scope" },
        { code: "info_modal_view_contract_name" },
        { code: "info_modal_view_contract_id" },
        { code: "info_modal_view_dsm_id" },
        { code: "info_modal_view_total_hours" },
        { code: "info_modal_view_squad" },
        // Permissões das abas da modal
        { code: "view_info_modal_tab_temporal_info" },
        { code: "view_info_modal_tab_general_info" },
        { code: "view_info_modal_tab_financial_info" },
        { code: "view_info_modal_tab_client_info" },
        { code: "view_info_modal_tab_contract_team_info" },
        { code: "view_info_modal_tab_governance_team_info" }
      ]
    },
    
    // Arquivo
    {
      page: "Arquivo",
      actions: [
        { code: "view_files" },
        { code: "upload_file" },
        { code: "download_file" },
        { code: "delete_file" },
        { code: "view_file_details" },
        { code: "manage_file_permissions" },
        { code: "edit_tags" },
        { code: "send_email" },
        { code: "edit_file" }
      ]
    },
    
    // MFA
    {
      page: "MFA",
      actions: [
        { code: "view_usuario" },
        { code: "view_portal" },
        { code: "view_reset" },
        { code: "create_mfa_secret" },
        { code: "edit_mfa_secret" },
        { code: "delete_mfa_secret" },
        { code: "view_mfa_users" }
      ]
    },
    
    // Auditoria
    {
      page: "Auditoria",
      actions: [
        { code: "view_audits" },
        { code: "export_audits" },
        { code: "filter_audits" },
        { code: "view_audit_details" }
      ]
    },
    
    // Billing
    {
      page: "Billing",
      actions: [
        { code: "view_billing" },
        { code: "export_billing" },
        { code: "manage_billing" },
        { code: "view_billing_details" }
      ]
    }
  ]
};

/**
 * Criar registro de permissão padrão na base de dados
 */
export async function createDefaultPermissionRecord() {
  try {
    console.log('🔧 Criando registro de permissão padrão...');
    
    const tableName = `${process.env.REACT_APP_STAGE}-permissions`;
    
    await dynamoPost(tableName, DEFAULT_PERMISSION_RECORD);
    
    console.log('✅ Registro de permissão padrão criado com sucesso!');
    console.log('📋 Detalhes:', {
      id: DEFAULT_PERMISSION_RECORD.id,
      name: DEFAULT_PERMISSION_RECORD.name_permission,
      pages: DEFAULT_PERMISSION_RECORD.permissions.length,
      totalActions: DEFAULT_PERMISSION_RECORD.permissions.reduce(
        (total, page) => total + page.actions.length, 0
      )
    });
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao criar registro de permissão padrão:', error);
    throw error;
  }
}

/**
 * Verificar se registro de permissão padrão existe
 */
export async function checkDefaultPermissionExists() {
  try {
    const { dynamoGetById } = await import('../service/apiDsmDynamo');
    const tableName = `${process.env.REACT_APP_STAGE}-permissions`;
    
    const data = await dynamoGetById(tableName, "1");
    return !!data;
  } catch (error) {
    return false;
  }
}

/**
 * Função principal para resolver problema de permissões
 */
export async function ensureDefaultPermissionExists() {
  try {
    console.log('🔍 Verificando se registro de permissão padrão existe...');
    
    const exists = await checkDefaultPermissionExists();
    
    if (exists) {
      console.log('✅ Registro de permissão padrão já existe!');
      return true;
    }
    
    console.log('⚠️ Registro não existe, criando...');
    await createDefaultPermissionRecord();
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao garantir existência do registro padrão:', error);
    return false;
  }
}

export default {
  DEFAULT_PERMISSION_RECORD,
  createDefaultPermissionRecord,
  checkDefaultPermissionExists,
  ensureDefaultPermissionExists
};
