/**
 * Configuração específica para desenvolvimento
 * Agora com suporte a configuração dinâmica da API
 */

import { dynamicApiConfig } from '../services/dynamicApiConfig';

export const getApiUrl = async () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    // Em desenvolvimento, usar proxy local
    console.log('🔧 Desenvolvimento: Usando proxy local http://localhost:8000/dev');
    return 'http://localhost:8000/dev';
  }

  // Em produção, tentar usar configuração dinâmica
  try {
    if (dynamicApiConfig.isReady()) {
      const config = dynamicApiConfig.getConfig();
      console.log('🔧 Produção: Usando URL dinâmica da API:', config.axios.baseURL);
      return config.axios.baseURL;
    }
  } catch (error) {
    console.warn('⚠️ Configuração dinâmica não disponível, usando fallback');
  }

  // Fallback: usar URL real da API Lambda
  return 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';
};

export const getCognitoUrl = async () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    // Em desenvolvimento, usar proxy local
    console.log('🔧 Desenvolvimento: Usando proxy Cognito /cognito-api');
    return '/cognito-api';
  }

  // Em produção, tentar obter configuração dinâmica primeiro
  try {
    if (dynamicApiConfig.isReady()) {
      const config = dynamicApiConfig.getConfig();
      const cognitoUrl = `${config.axios.baseURL}/cognito`;
      console.log('🔧 Produção: Usando URL dinâmica do Cognito:', cognitoUrl);
      return cognitoUrl;
    }
  } catch (error) {
    console.warn('⚠️ Configuração dinâmica não disponível, usando fallback');
  }

  // Fallback: usar URL real da API Lambda
  return 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev/cognito';
};

export const getDevConfig = async () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Obter URLs de forma assíncrona
  const apiUrl = await getApiUrl();
  const cognitoUrl = await getCognitoUrl();

  return {
    isDevelopment,
    apiUrl,
    cognitoUrl,
    useProxy: isDevelopment,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };
};

export const createDevAxiosConfig = (url) => {
  const config = getDevConfig();

  return {
    baseURL: url || config.apiUrl,
    timeout: config.timeout,
    headers: config.headers
  };
};

export const logDevInfo = () => {
  const config = getDevConfig();
};

export default {
  getApiUrl,
  getCognitoUrl,
  getDevConfig,
  createDevAxiosConfig,
  logDevInfo
};
