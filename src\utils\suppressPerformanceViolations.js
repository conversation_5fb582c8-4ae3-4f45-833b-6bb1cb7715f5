/**
 * Supressão específica para violações de performance do Chrome DevTools
 * Essas violações são informativas e não afetam o funcionamento da aplicação
 */

// Interceptar e suprimir violações de performance (apenas se habilitado)
(function() {
  'use strict';

  // Verificar se a supressão está habilitada
  if (process.env.REACT_APP_SUPPRESS_PERFORMANCE_VIOLATIONS !== 'true') {
    return; // Não aplicar supressão se não estiver habilitada
  }
  
  // Armazenar referências originais
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;
  
  // Função para verificar se é uma violação de performance que deve ser suprimida
  function isPerformanceViolationToSuppress(message) {
    const messageStr = String(message);
    
    // Padrões específicos de violações de performance
    const patterns = [
      /\[Violation\].*handler took.*ms/i,
      /\[Violation\].*'message' handler took.*ms/i,
      /\[Violation\].*'loadend' handler took.*ms/i,
      /\[Violation\].*'load' handler took.*ms/i,
      /\[Violation\].*'click' handler took.*ms/i,
      /\[Violation\].*'change' handler took.*ms/i,
      /Forced reflow while executing JavaScript took.*ms/i,
      /\[Violation\] Forced reflow/i,
      /\[Violation\] Long task took.*ms/i,
      /\[Violation\] Added non-passive event listener/i
    ];
    
    return patterns.some(pattern => pattern.test(messageStr));
  }
  
  // Sobrescrever console.warn
  console.warn = function(...args) {
    const firstArg = args[0];
    
    if (isPerformanceViolationToSuppress(firstArg)) {
      return; // Suprimir a violação
    }
    
    // Para outros warnings, usar o console original
    return originalConsoleWarn.apply(console, args);
  };
  
  // Sobrescrever console.log (algumas violações vêm como log)
  console.log = function(...args) {
    const firstArg = args[0];
    
    if (isPerformanceViolationToSuppress(firstArg)) {
      return; // Suprimir a violação
    }
    
    // Para outros logs, usar o console original
    return originalConsoleLog.apply(console, args);
  };
  
  // Função para restaurar console original (para debugging)
  window.__restorePerformanceConsole = function() {
    console.warn = originalConsoleWarn;
    console.log = originalConsoleLog;
  };
  
})();

// Log de confirmação apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  console.log('⚡ Performance violations suppressed');
}
