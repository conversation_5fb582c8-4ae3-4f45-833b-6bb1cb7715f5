import React, { useMemo, useEffect, useState, useRef } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { format, isValid } from "date-fns";
import { Row, Col, Tag } from "antd";

import { Counter } from "../../components/Counter";
import { VisualizeInfo } from "../../components/Modals/Contracts/VisualizeInfo";
import { ExecutivesModal } from "../../components/Modals/Contracts/ExecutivesModal";
import { EditTableContractModal } from "../../components/Modals/Contracts/EditTableContractModal";

import { filterTableData } from "../../utils/filterTableData";
import * as controller from "../../controllers/contracts/contract-controller";
import { setContractFieldState } from "../../store/actions/contract-action";
import { InactiveContractReason } from "../../components/Modals/Contracts/InactiveContractReason";

import { DynamicTable } from "../../components/Table/DynamicTable/index";
import moment from "moment";
import { formatHourPoolData } from "../../components/Modals/Contracts/controllers/visualizeInfo";
import { ModalChangeContractHours } from "./components/modals-special-cases";

export const ContractTable = () => {
  const {
    activeContracts,
    inactiveContracts,
    active,
    permissions,
    executives,
    search,
    dtEnd,
    dtStart,
    contractTypes,
    selectedContract,
    selectedSquads,
    selectedPool,
    poolTypes,
    selectedExecutive,
  } = useSelector((state) => state.contract, shallowEqual);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);

    controller.getActiveContracts(setLoading);
    controller.getInactiveContracts();
    controller.getPermissions();
    controller.getExecutives();
    controller.getWallets();
    controller.getUsers();

    return () => {
      setContractFieldState({ field: "active", value: 1 });
      setContractFieldState({ field: "search", value: "" });
      setContractFieldState({ field: "dtEnd", value: null });
      setContractFieldState({ field: "dtStart", value: null });
      setContractFieldState({ field: "selectedContract", value: 0 });
      setContractFieldState({ field: "selectedPool", value: 0 });
    };
  }, []);

  const filterContractsByFilter = useMemo(() => {
    console.log('🔍 Filtro por status:', {
      active,
      activeContracts: activeContracts?.length || 0,
      inactiveContracts: inactiveContracts?.length || 0
    });

    switch (active) {
      case "inactive":
        return inactiveContracts;
      case "active":
        return activeContracts;
      default:
        return [...activeContracts, ...inactiveContracts];
    }
  }, [activeContracts, inactiveContracts, active]);

  const filterContractsBySearch = useMemo(() => {
    const listStr = JSON.stringify(filterContractsByFilter);
    let listObj = JSON.parse(listStr);

    let list = filterTableData({
      search,
      searchFields: [
        "name",
        "formactDate",
        "identifications",
        "customer_itsm",
        "dsm_id",
      ],
      data: listObj.map((contract) => {
        const formactDate = isValid(new Date(contract.created_at))
          ? format(new Date(contract.created_at), "dd/MM/yyyy")
          : "Data inválida";

        const item = { ...contract, formactDate };
        return item;
      }),
    });

    return list;
  }, [filterContractsByFilter, search, dtEnd, dtStart]);

  const contractsFilteredByDate = useMemo(() => {
    let list = filterContractsBySearch;
    list = list.filter((data) => {
      const contractCreation = moment(data?.created_at).date(15);

      if (dtEnd !== null && dtStart !== null) {
        return (
          contractCreation >= dtStart.date(1) &&
          contractCreation <= dtEnd.date(28)
        );
      }
      return true;
    });

    return list;
  }, [dtEnd, dtStart, filterContractsBySearch]);

  const contractsFilteredByConsumptionType = useMemo(() => {
    let list = contractsFilteredByDate;
    const contractType = contractTypes.find(
      (c) => c.value === selectedContract
    );
    if (contractType.label !== "Todos") {
      list = contractsFilteredByDate.filter((contract) => {
        return (
          contract.type_hours?.toUpperCase() ===
          contractType.label.toUpperCase()
        );
      });
    }
    return list;
  }, [contractTypes, selectedContract, contractsFilteredByDate]);

  const contractsFilteredBySquads = useMemo(() => {
    let list = contractsFilteredByConsumptionType;
    const squadList = selectedSquads.map((squad) => squad.label);

    if (squadList.includes("Todos") || squadList.length === 0)
      return contractsFilteredByConsumptionType;

    list = contractsFilteredByConsumptionType.filter((contract) =>
      squadList.includes(contract.squad)
    );

    return list;
  }, [selectedSquads, contractsFilteredByConsumptionType]);

  const contractsFilteredByPoolType = useMemo(() => {
    let list = contractsFilteredBySquads;
    const poolType = poolTypes.find((c) => c.value === selectedPool);
    if (poolType.label !== "Todos") {
      list = contractsFilteredBySquads.filter((contract) => {
        if (contract.pool_type) {
          return formatHourPoolData(contract.pool_type) === poolType.label;
        }
      });
    }
    return list;
  }, [selectedPool, contractsFilteredBySquads]);

  // ✅ useEffect separado para atualizar Redux sem causar setState durante render
  useEffect(() => {
    setContractFieldState({ field: "filteredContracts", value: contractsFilteredByPoolType });
  }, [contractsFilteredByPoolType]);

  const contractsFilteredByExecutive = useMemo(() => {
    let list = contractsFilteredByPoolType;
    if (selectedExecutive && selectedExecutive !== "") {
      list = list.filter((contract) =>
        contract?.executives?.some(
          (executive) => executive?.email === selectedExecutive
        )
      );
    }

    console.log('🔍 Filtro final (contractsFilteredByExecutive):', {
      input: contractsFilteredByPoolType?.length || 0,
      selectedExecutive,
      output: list?.length || 0,
      sample: list?.slice(0, 2)?.map(c => ({ id: c.id, customer_name: c.customer_name }))
    });

    return list;
  }, [selectedExecutive, contractsFilteredByPoolType]);

  // Ref to track previous filtered contracts to avoid unnecessary updates
  const previousFilteredContractsRef = useRef();

  // Update filteredContracts in store when the filtered list changes
  // Using useEffect with ref comparison to avoid infinite loops
  useEffect(() => {
    // Only update if the array actually changed (deep comparison)
    const hasChanged = JSON.stringify(contractsFilteredByExecutive) !==
                      JSON.stringify(previousFilteredContractsRef.current);

    if (hasChanged) {
      previousFilteredContractsRef.current = contractsFilteredByExecutive;
      // ✅ Usar setTimeout para evitar setState durante render
      setTimeout(() => {
        setContractFieldState({ field: "filteredContracts", value: contractsFilteredByExecutive });
      }, 0);
    }
  }, [contractsFilteredByExecutive]);

  async function getCustomerContracts() {
    if (active) await controller.getActiveContracts();
    else await controller.getInactiveContracts();
  }

  const columns = [
    {
      code: "view_date",
      title: "Data de Criação",
      dataIndex: "created_at",
      align: "center",
      render: (date) =>
        isValid(new Date(date))
          ? format(new Date(date), "dd/MM/yyyy")
          : "Data inválida",
      defaultSortOrder: "descend",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) =>
        isValid(new Date(a?.created_at))
          ? new Date(a?.created_at) - new Date(b?.created_at)
          : "",
      width: "155px",
    },
    {
      code: "view_dsm_id",
      title: "DSM ID",
      align: "center",
      dataIndex: "dsm_id",
      render: (field, item) => item.dsm_id || "-",
      sorter: (a, b) => a.dsm_id?.localeCompare(b.dsm_id),
      sortDirections: ["descend", "ascend"],
      width: "2%",
    },
    // {
    //   code: "view_crm",
    //   title: "CRM",
    //   dataIndex: ["identifications", "crm_id"],
    //   align: "center",
    //   sorter: (a, b) => a.identifications?.crm_id - b.identifications?.crm_id,
    //   sortDirections: ["descend", "ascend"],
    //   width: "1%",
    // },
    // {
    //   code: "view_itsm",
    //   title: "ITSM",
    //   dataIndex: ["identifications", "itsm_id"],
    //   align: "center",
    //   sorter: (a, b) => a.identifications?.itsm_id - b.identifications?.itsm_id,
    //   sortDirections: ["descend", "ascend"],
    //   width: "1%",
    // },
    {
      code: "view_client",
      title: "Cliente",
      align: "center",
      dataIndex: "customer_dsm_id",
      sorter: (a, b) =>
        a.customer_dsm_id
          ? a?.customer_dsm_id - b?.customer_dsm_id
          : a?.customer_itsm - b?.customer_itsm,
      render: (field, item) => item.customer_dsm_id || item.customer_itsm,
      sortDirections: ["descend", "ascend"],
      width: "1%",
    },
    {
      code: "view_name",
      title: "Nome",
      dataIndex: "name",
      sorter: (a, b) => a?.name?.localeCompare(b?.name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_type_hours",
      title: "Tipo de Consumo",
      dataIndex: "type_hours",
      sorter: (a, b) => a?.type_hours?.localeCompare(b?.type_hours),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_pool_type",
      title: "Tipo de Horas",
      dataIndex: "pool_type",
      sorter: (a, b) => a?.pool_type?.localeCompare(b?.pool_type),
      sortDirections: ["descend", "ascend"],
      render: (e) => e && formatHourPoolData(e),
    },
    {
      code: "view_squad",
      title: "Squad",
      dataIndex: "squad",
      sorter: (a, b) => a?.squad?.localeCompare(b?.squad),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_active",
      title: "Ativo",
      dataIndex: "active",
      width: "1%",
      render: (e) => {
        if (e === 1) {
          return <Tag color="green">Ativo</Tag>;
        } else {
          return <Tag color="red">Inativo</Tag>;
        }
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <EditTableContractModal
            contract={item}
            permissions={permissions}
            getCustomerContracts={getCustomerContracts}
            client={item.customer}
          />
        );
      },
    },
    {
      code: "view_change_hours",
      title: "Alterar Horas",
      dataIndex: "contract_hours",
      align: "center",
      width: "1%",
      render: (id, item) => {
        return (
          <ModalChangeContractHours contractId={item.id} contractData={item} />
        );
      },
    },
    {
      code: "view_executives",
      title: "Executivos",
      dataIndex: "executives",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <ExecutivesModal
                getCustomerContracts={getCustomerContracts}
                permissions={permissions}
                executivesData={{ data: executives }}
                executives={field}
                contract={item}
              />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_info",
      title: "Informações",
      dataIndex: "id",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <VisualizeInfo contract={item} userPermissions={permissions} />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "active",
      width: "1%",
      render: (field, item) => {
        return (
          <InactiveContractReason
            contract={item}
            field={field}
            loading={loading}
            setLoading={setLoading}
            controller={controller}
          />
        );
      },
    },
  ];

  return (
    <>
      <Counter tableData={contractsFilteredByExecutive} />
      <DynamicTable
        scroll={{ x: "100%" }}
        style={{ minWidth: "100%" }}
        columns={(() => {
          const permissionCodes = permissions.map((permission) => permission.code);
          const filteredColumns = columns.filter((e) => permissionCodes.includes(e.code));

          console.log('🔍 Colunas da tabela:', {
            totalColumns: columns.length,
            permissions: permissionCodes,
            filteredColumns: filteredColumns.length,
            columnCodes: filteredColumns.map(c => c.code)
          });

          return filteredColumns;
        })()}
        rowKey="id"
        loading={loading}
        pagination={{
          data: [],
        }}
        data={contractsFilteredByExecutive}
      />
    </>
  );
};
