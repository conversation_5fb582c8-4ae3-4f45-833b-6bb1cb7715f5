import { useEffect, useCallback, useRef } from 'react';

/**
 * Performance monitoring hook
 * Tracks component render times, API calls, and user interactions
 */
export const usePerformanceMonitor = (componentName) => {
  const renderStartTime = useRef(null);
  const apiCallTimes = useRef(new Map());

  // Track component render time
  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      if (renderStartTime.current) {
        const renderTime = performance.now() - renderStartTime.current;
        
        if (process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true') {
          console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`);
          
          // Report to analytics service if available
          if (window.gtag) {
            window.gtag('event', 'component_render_time', {
              component_name: componentName,
              render_time: Math.round(renderTime),
            });
          }
        }
      }
    };
  }, [componentName]);

  // Track API call performance
  const trackApiCall = useCallback((apiName, startTime) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    apiCallTimes.current.set(apiName, duration);
    
    if (process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true') {
      console.log(`[Performance] API call ${apiName}: ${duration.toFixed(2)}ms`);
      
      // Report to analytics service
      if (window.gtag) {
        window.gtag('event', 'api_call_time', {
          api_name: apiName,
          duration: Math.round(duration),
        });
      }
    }
    
    return duration;
  }, []);

  // Start API call tracking
  const startApiCall = useCallback((apiName) => {
    const startTime = performance.now();
    
    return {
      end: () => trackApiCall(apiName, startTime),
      startTime
    };
  }, [trackApiCall]);

  // Track user interactions
  const trackUserInteraction = useCallback((interactionType, elementId) => {
    if (process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true') {
      console.log(`[Performance] User interaction: ${interactionType} on ${elementId}`);
      
      if (window.gtag) {
        window.gtag('event', 'user_interaction', {
          interaction_type: interactionType,
          element_id: elementId,
        });
      }
    }
  }, []);

  // Get performance metrics
  const getMetrics = useCallback(() => {
    return {
      componentName,
      apiCallTimes: Object.fromEntries(apiCallTimes.current),
      renderTime: renderStartTime.current ? performance.now() - renderStartTime.current : null,
    };
  }, [componentName]);

  return {
    startApiCall,
    trackApiCall,
    trackUserInteraction,
    getMetrics,
  };
};

/**
 * Web Vitals monitoring hook
 */
export const useWebVitals = () => {
  useEffect(() => {
    if (process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true') {
      // Dynamic import to avoid loading if not needed
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      }).catch(error => {
        console.warn('Failed to load web-vitals:', error);
      });
    }
  }, []);
};

/**
 * Memory usage monitoring hook
 */
export const useMemoryMonitor = (intervalMs = 30000) => {
  useEffect(() => {
    if (process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING !== 'true') {
      return;
    }

    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = performance.memory;
        const memoryInfo = {
          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576), // MB
          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576), // MB
          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        };
        
        console.log('[Performance] Memory usage:', memoryInfo);
        
        // Alert if memory usage is high
        const usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        if (usagePercentage > 80) {
          console.warn(`[Performance] High memory usage: ${usagePercentage.toFixed(1)}%`);
        }
        
        // Report to analytics
        if (window.gtag) {
          window.gtag('event', 'memory_usage', {
            used_heap_size: memoryInfo.usedJSHeapSize,
            usage_percentage: Math.round(usagePercentage),
          });
        }
      }
    };

    const interval = setInterval(checkMemoryUsage, intervalMs);
    
    // Initial check
    checkMemoryUsage();
    
    return () => clearInterval(interval);
  }, [intervalMs]);
};

/**
 * Bundle size analyzer (development only)
 */
export const useBundleAnalyzer = () => {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && 
        process.env.REACT_APP_ENABLE_BUNDLE_ANALYZER === 'true') {
      
      // Log loaded modules
      const loadedModules = Object.keys(window.webpackChunkName || {});
      console.log('[Performance] Loaded modules:', loadedModules);
      
      // Monitor chunk loading
      if (window.__webpack_require__) {
        const originalRequire = window.__webpack_require__;
        window.__webpack_require__ = function(moduleId) {
          const startTime = performance.now();
          const result = originalRequire.call(this, moduleId);
          const loadTime = performance.now() - startTime;
          
          if (loadTime > 10) { // Log slow module loads
            console.log(`[Performance] Slow module load: ${moduleId} (${loadTime.toFixed(2)}ms)`);
          }
          
          return result;
        };
      }
    }
  }, []);
};

export default usePerformanceMonitor;
