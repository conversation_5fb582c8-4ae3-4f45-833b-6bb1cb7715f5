import axios from "axios";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export const dsmApiProvider = (jwt = "") => {
  if (jwt) {
    return axios.create({
      baseURL: process.env.REACT_APP_API_PERMISSION || "",
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    });
  }

  return authService.createSimpleAxios(process.env.REACT_APP_API_PERMISSION || "");
};

export const dsmApiProviderDynamic = async (jwt = "") => {
  const apiUrl = await getApiUrl();
  console.log('🔍 dsmApiProviderDynamic: Usando URL:', apiUrl);

  if (jwt) {
    return axios.create({
      baseURL: apiUrl,
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    });
  }

  // Caso contrário, usa o serviço de autenticação sem credentials
  return authService.createSimpleAxios(apiUrl);
};

export function getDsmHeader(tableName = "", jwt = "") {
  const token = jwt ? jwt : authService.getToken();

  let headers = {
    Authorization: `Bearer ${token}`,
  };

  if (tableName) {
    headers["dynamodb"] = tableName;
  }

  return headers;
}
