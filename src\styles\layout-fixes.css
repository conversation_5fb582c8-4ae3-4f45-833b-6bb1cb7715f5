/* ✅ Correções de Layout e Tipografia - DSM Frontend */
/* Arquivo para normalizar diferenças visuais entre versões */

/* ========================================
   NORMALIZAÇÃO DE TIPOGRAFIA
   ======================================== */

/* Peso da fonte padrão para componentes */
.ant-typography,
.ant-typography p,
.ant-typography div,
.ant-typography span {
  font-weight: 400 !important;
}

/* Títulos com peso correto */
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5,
.ant-typography h6 {
  font-weight: 600 !important;
}

/* Texto strong apenas quando necessário */
.ant-typography strong,
.ant-typography .ant-typography-strong {
  font-weight: 600 !important;
}

/* ========================================
   NORMALIZAÇÃO DE TAMANHOS
   ======================================== */

/* Tamanho de fonte padrão */
.ant-typography,
.ant-table,
.ant-form,
.ant-input,
.ant-select,
.ant-btn {
  font-size: 14px !important;
  line-height: 1.5714285714285714 !important;
}

/* Botões com tamanho correto */
.ant-btn {
  height: 32px !important;
  padding: 4px 15px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Inputs com tamanho correto */
.ant-input,
.ant-select-selector {
  height: 32px !important;
  padding: 4px 11px !important;
  font-size: 14px !important;
}

/* ========================================
   NORMALIZAÇÃO DE ESPAÇAMENTOS
   ======================================== */

/* Espaçamento de cards */
.ant-card {
  margin-bottom: 16px !important;
}

.ant-card-body {
  padding: 24px !important;
}

/* Espaçamento de formulários */
.ant-form-item {
  margin-bottom: 24px !important;
}

.ant-form-item-label {
  padding-bottom: 8px !important;
}

/* Espaçamento de tabelas */
.ant-table-thead > tr > th {
  padding: 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.ant-table-tbody > tr > td {
  padding: 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* ========================================
   CORREÇÕES ESPECÍFICAS DE COMPONENTES
   ======================================== */

/* Paginação normalizada */
.ant-pagination {
  font-size: 14px !important;
}

.ant-pagination-item {
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

.ant-pagination-item a {
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Menu lateral normalizado */
.ant-menu {
  font-size: 14px !important;
}

.ant-menu-item {
  height: 40px !important;
  line-height: 40px !important;
  font-weight: 400 !important;
}

/* Tabs normalizadas */
.ant-tabs-tab {
  padding: 12px 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.ant-tabs-tab-active {
  font-weight: 600 !important;
}

/* Modal normalizado */
.ant-modal-header {
  padding: 16px 24px !important;
}

.ant-modal-body {
  padding: 24px !important;
}

.ant-modal-title {
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* ========================================
   CORREÇÕES DE DENSIDADE VISUAL
   ======================================== */

/* Reduzir densidade se necessário */
.ant-table-small .ant-table-thead > tr > th,
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px !important;
}

/* Espaçamento de listas */
.ant-list-item {
  padding: 12px 0 !important;
}

/* Espaçamento de descriptions */
.ant-descriptions-item-label {
  font-weight: 600 !important;
}

.ant-descriptions-item-content {
  font-weight: 400 !important;
}

/* ========================================
   CORREÇÕES DE BORDAS E RAIOS
   ======================================== */

/* Bordas consistentes */
.ant-btn,
.ant-input,
.ant-select-selector,
.ant-card {
  border-radius: 6px !important;
}

/* ========================================
   CORREÇÕES DE CORES DE TEXTO
   ======================================== */

/* Cor de texto padrão */
.ant-typography,
.ant-table,
.ant-form,
.ant-menu {
  color: #000000d9 !important;
}

/* Cor de texto secundário */
.ant-typography-caption,
.ant-form-item-extra {
  color: #00000073 !important;
}

/* ========================================
   CORREÇÕES ESPECÍFICAS PARA DSM
   ======================================== */

/* Remover negritos desnecessários em tabelas */
.ant-table-thead > tr > th .ant-table-column-title {
  font-weight: 600 !important;
}

.ant-table-tbody > tr > td {
  font-weight: 400 !important;
}

/* Normalizar peso de fonte em cards */
.ant-card-head-title {
  font-weight: 600 !important;
  font-size: 16px !important;
}

/* Normalizar estatísticas */
.ant-statistic-title {
  font-weight: 400 !important;
  font-size: 14px !important;
}

.ant-statistic-content {
  font-weight: 600 !important;
}

/* ========================================
   CORREÇÕES DE RESPONSIVIDADE
   ======================================== */

/* Garantir que os componentes não fiquem muito grandes em telas pequenas */
@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px !important;
  }
  
  .ant-modal-body {
    padding: 16px !important;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px !important;
  }
}

/* ========================================
   RESET DE ESTILOS CONFLITANTES
   ======================================== */

/* Remover estilos que podem estar causando problemas */
* {
  box-sizing: border-box;
}

/* Garantir que não há estilos globais afetando o peso da fonte */
body, html {
  font-weight: 400 !important;
}
