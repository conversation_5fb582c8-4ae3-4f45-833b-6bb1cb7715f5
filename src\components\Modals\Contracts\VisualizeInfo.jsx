import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Button, Row, Col, List, Tag, Tabs, Divider } from "antd";
import * as modalViewByPermission from "../../../constants/contractsInfoPerPermission";
import { tabsData } from "./constants/visualizeInfoData";
import * as controller from "../../../controllers/contracts/contract-controller";
import { SearchInput } from "../../SearchInput";
import {
  dynamoGetById,
  getItemsByDynamicIndex,
} from "../../../service/apiDsmDynamo";
import { ExpandableText } from "../../ExpandableText/ExpandableText";
import {
  filterSignatureDateIfContractIsNotSigned,
  formatHourPoolData,
  formatViewDateFields,
} from "./controllers/visualizeInfo";

export const VisualizeInfo = (props) => {
  const [ellipsis, setEllipsis] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [contract, setContract] = useState([]);
  const [searchField, setSearchField] = useState("");
  const { id, customer_id } = props.contract;
  const { userPermissions } = props;

  // Normalizar permissões para garantir estrutura correta
  const normalizedPermissions = useMemo(() => {
    if (!userPermissions) return [];

    // Se userPermissions é um array direto
    if (Array.isArray(userPermissions)) {
      return userPermissions;
    }

    // Se userPermissions tem uma propriedade data
    if (userPermissions.data && Array.isArray(userPermissions.data)) {
      return userPermissions.data;
    }

    // Fallback: retornar array vazio
    return [];
  }, [userPermissions]);
  const getWindowDimensions = () => {
    const { innerWidth: width, innerHeight: height } = window;
    return { width, height };
  };

  const useWindowDimensions = () => {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions()
    );

    useEffect(() => {
      const handleResize = () => setWindowDimensions(getWindowDimensions());

      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }, []);

    return windowDimensions;
  };

  const { width } = useWindowDimensions();

  useEffect(() => {
    if (showModal && id) {
      getContractInfo();
    }
  }, [showModal, id]);

  const formatContract = (contract, customer) => {
    let formattedContract = {};
    const customerInfo = {
      customer_name: customer?.names?.name,
      customer_cnpj: customer?.cnpj,
    };
    for (let key of Object.entries(contract)) {
      if (typeof key[1] === "object") {
        if (key[1]) {
          Object.entries(key[1]).map((item) => {
            if (key[0] !== "customer") {
              formattedContract[item[0]] = item[1];
            }
          });
        }
      }
    }
    formattedContract = {
      ...formattedContract,
      ...contract,
      ...customerInfo,
    };

    formattedContract = Object.entries(formattedContract).filter(
      (item) => item[1] !== null
    );
    return formattedContract;
  };

  async function getCurentCustomer() {
    try {
      if (!customer_id) {
        console.warn('VisualizeInfo: customer_id não fornecido');
        return null;
      }

      const customer = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-customers`,
        customer_id
      );

      if (process.env.NODE_ENV === 'development') {
        console.log('VisualizeInfo: Cliente carregado:', {
          customerId: customer_id,
          hasCustomer: !!customer,
          customerName: customer?.names?.name
        });
      }

      return customer;
    } catch (error) {
      console.error('Erro ao buscar cliente:', error);
      return null;
    }
  }

  async function getContractInfo() {
    let updatedHoursData = [];
    const customer = await getCurentCustomer();
    if (showModal && id) {
      const contract = await controller.getContract(id);

      // Debug em desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.log('VisualizeInfo: Debug de dados:', {
          contractId: id,
          hasContract: !!contract,
          hasCustomer: !!customer,
          permissionsCount: normalizedPermissions.length,
          permissionsSample: normalizedPermissions.slice(0, 3),
          contractKeys: contract ? Object.keys(contract).slice(0, 10) : 'N/A'
        });
      }
      if (contract) {
        if (contract.hasChangedHours) {
          updatedHoursData = await getItemsByDynamicIndex(
            `${process.env.REACT_APP_STAGE}-consumption-hours`,
            "contract_id",
            id
          );
          updatedHoursData = updatedHoursData
            .sort((a, b) =>
              b.updated_consumption_hours.localeCompare(
                a.updated_consumption_hours
              )
            )
            ?.shift();
        }

        let formattedContract = formatContract(contract, customer);
        formattedContract = formattedContract.filter(
          (item) => typeof item[1] !== "object"
        );

        // Processar permissões de forma mais robusta
        const processedContract = [];

        formattedContract.forEach((item) => {
          modalViewByPermission.contractsView.forEach((permission) => {
            if (permission.key === item[0]) {
              // Verificar permissões: se não há permissões ou se tem a permissão específica
              const hasPermission = normalizedPermissions.length === 0 ||
                                   normalizedPermissions.find(perm => perm.code === permission.permissionCode);

              if (hasPermission) {
                try {
                  const content = permission.fieldName.toLowerCase().includes("data")
                    ? formatViewDateFields(item[1], permission.fieldName)
                    : item[1];

                  processedContract.push({
                    fieldName: permission.fieldName,
                    content: content,
                    permissionCode: permission.permissionCode,
                    key: permission.key,
                  });
                } catch (error) {
                  console.warn('Erro ao processar campo:', permission.fieldName, error);
                  // Adicionar campo mesmo com erro, mas com conteúdo original
                  processedContract.push({
                    fieldName: permission.fieldName,
                    content: item[1],
                    permissionCode: permission.permissionCode,
                    key: permission.key,
                  });
                }
              }
            }
          });
        });

        formattedContract = processedContract;
        formattedContract = formattedContract.filter((item) => !item[0]);
        if (contract.hasChangedHours)
          formattedContract.map((item) => {
            if (item.key === "total_hours")
              item.content = updatedHoursData.new_hours;
          });

        // Debug em desenvolvimento
        if (process.env.NODE_ENV === 'development') {
          console.log('VisualizeInfo: Contrato processado:', {
            originalFieldsCount: Object.keys(contract).length,
            processedFieldsCount: formattedContract.length,
            fieldsWithPermissions: formattedContract.map(f => ({ key: f.key, fieldName: f.fieldName })),
            availablePermissions: modalViewByPermission.contractsView.length,
            userPermissionsCount: normalizedPermissions.length
          });
        }

        setContract(formattedContract);
      } else {
        // Se não há contrato, definir array vazio
        if (process.env.NODE_ENV === 'development') {
          console.warn('VisualizeInfo: Contrato não encontrado para ID:', id);
        }
        setContract([]);
      }
    } else {
      setContract([]);
    }
  }

  const filteredFields = useMemo(() => {
    let currentContract = filterSignatureDateIfContractIsNotSigned(contract);
    return currentContract.filter((item) =>
      item.fieldName.toLowerCase().includes(searchField.toLowerCase())
    );
  }, [searchField, contract]);

  return (
    <>
      <Button
        style={{ padding: "0" }}
        type="text"
        onClick={async () => {
          setShowModal(true);
          setEllipsis(true);
        }}
      >
        <Tag color="#0f9347">Visualizar</Tag>
      </Button>
      <Modal
        width={width > 1000 ? "60%" : "100%"}
        title="Informações do Contrato"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[<Button onClick={() => setShowModal(false)}>Fechar</Button>]}
      >
        <SearchInput
          onChange={(value) => setSearchField(value)}
          placeholder="Pesquisar por campo"
          style={{ marginBottom: "10px" }}
        />
        <Tabs
          type="card"
          defaultActiveKey="general_info"
          items={tabsData.map((tab) => {
            return {
              disabled: normalizedPermissions.length > 0 && !normalizedPermissions.some(
                (permission) => permission.code === tab.permissionCode
              ),
              key: tab.key,
              label: tab.title,
              children: (
                <Row align="middle" justify="center">
                  <Col span={24}>
                    <List
                      grid={{ gutter: 16, column: 3 }}
                      itemLayout="horizontal"
                      loading={contract?.length === 0}
                      pagination={{ pageSize: 10 }}
                      dataSource={filteredFields.filter((item) =>
                        tab.fieldKeys.includes(item.key)
                      )}
                      renderItem={(item, index) => {
                        return (
                          <List.Item
                            extra={<Divider />}
                            key={index}
                            onClick={() => {
                              if (item.content.length > 50)
                                setEllipsis(!ellipsis);
                            }}
                          >
                            <List.Item.Meta
                              title={item.fieldName}
                              description={
                                <ExpandableText
                                  cursor={
                                    item.content.length > 50
                                      ? "pointer"
                                      : "auto"
                                  }
                                  expanded={ellipsis}
                                  itemId={item.key}
                                  text={
                                    item.key === "pool_type"
                                      ? formatHourPoolData(item.content)
                                      : item.key === "installmentsValue"
                                      ? Number(item.content || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })
                                      : item.content
                                  }
                                  rows={1}
                                  symbol="mais"
                                />
                              }
                            />
                          </List.Item>
                        );
                      }}
                    />
                  </Col>
                </Row>
              ),
            };
          })}
        ></Tabs>
      </Modal>
    </>
  );
};
