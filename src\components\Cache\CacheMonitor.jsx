import React, { useState } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Button,
  Table,
  Space,
  Tag,
  Progress,
  Tooltip,
  Modal,
  message,
  Tabs
} from 'antd';
import {
  DeleteOutlined,
  ReloadOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useCacheStats, useCacheContext, useOfflineCache } from '../../contexts/CacheContext';

const { TabPane } = Tabs;

/**
 * Componente para monitorar e gerenciar cache
 * Fornece interface visual para estatísticas e controle de cache
 */
const CacheMonitor = ({ showDetails = true, compact = false }) => {
  const [selectedCache, setSelectedCache] = useState('memory');
  const [showClearModal, setShowClearModal] = useState(false);
  const { stats, totalSize, totalEntries, breakdown, updateStats } = useCacheStats();
  const { clearCache, clearAllCaches, cleanup, invalidatePattern } = useCacheContext();
  const { isOnline } = useOfflineCache();

  /**
   * Formata tamanho em bytes
   */
  const formatSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Formata tempo relativo
   */
  const formatTimeAgo = (date) => {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  /**
   * Obtém cor baseada na idade da entrada
   */
  const getAgeColor = (created, expiry) => {
    const now = new Date();
    const age = now - created;
    const ttl = expiry - created;
    const ratio = age / ttl;

    if (ratio > 0.8) return 'red';
    if (ratio > 0.5) return 'orange';
    return 'green';
  };

  /**
   * Colunas da tabela de entradas
   */
  const columns = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>{text}</code>
        </Tooltip>
      )
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size',
      width: 80,
      render: (size) => formatSize(size),
      sorter: (a, b) => a.size - b.size
    },
    {
      title: 'Age',
      dataIndex: 'created',
      key: 'age',
      width: 100,
      render: (created, record) => (
        <Tag color={getAgeColor(created, record.expiry)}>
          {formatTimeAgo(created)}
        </Tag>
      ),
      sorter: (a, b) => b.created - a.created
    },
    {
      title: 'TTL',
      dataIndex: 'ttl',
      key: 'ttl',
      width: 80,
      render: (ttl) => `${Math.round(ttl / 1000)}s`
    },
    {
      title: 'Expires',
      dataIndex: 'expiry',
      key: 'expiry',
      width: 100,
      render: (expiry) => {
        const now = new Date();
        const remaining = expiry - now;
        if (remaining <= 0) {
          return <Tag color="red">Expired</Tag>;
        }
        return <Tag color="blue">{formatTimeAgo(new Date(now.getTime() + remaining))}</Tag>;
      }
    }
  ];

  /**
   * Executa limpeza de cache
   */
  const handleCleanup = async () => {
    try {
      const results = cleanup();
      const total = Object.values(results).reduce((sum, count) => sum + count, 0);
      message.success(`Removed ${total} expired entries`);
    } catch (error) {
      message.error('Failed to cleanup cache');
    }
  };

  /**
   * Limpa cache específico
   */
  const handleClearCache = (type) => {
    Modal.confirm({
      title: `Clear ${type} cache?`,
      content: 'This action cannot be undone.',
      onOk: () => {
        try {
          if (type === 'all') {
            clearAllCaches();
            message.success('All caches cleared');
          } else {
            clearCache(type);
            message.success(`${type} cache cleared`);
          }
        } catch (error) {
          message.error('Failed to clear cache');
        }
      }
    });
  };

  /**
   * Renderiza estatísticas resumidas
   */
  const renderSummary = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Statistic
          title="Total Entries"
          value={totalEntries}
          prefix={<InfoCircleOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="Total Size"
          value={formatSize(totalSize)}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="Memory Usage"
          value={breakdown.memory.percentage}
          precision={1}
          suffix="%"
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="Status"
          value={isOnline ? 'Online' : 'Offline'}
          prefix={isOnline ? <WifiOutlined /> : <DisconnectOutlined />}
          valueStyle={{ color: isOnline ? '#3f8600' : '#cf1322' }}
        />
      </Col>
    </Row>
  );

  /**
   * Renderiza breakdown por tipo de cache
   */
  const renderBreakdown = () => (
    <Row gutter={16}>
      {Object.entries(breakdown).map(([type, data]) => (
        <Col span={8} key={type}>
          <Card size="small">
            <Statistic
              title={`${type.charAt(0).toUpperCase() + type.slice(1)} Cache`}
              value={data.size}
              suffix="entries"
            />
            <Progress
              percent={data.percentage}
              size="small"
              status={data.percentage > 80 ? 'exception' : 'active'}
            />
            <div style={{ marginTop: 8 }}>
              <Button
                size="small"
                danger
                onClick={() => handleClearCache(type)}
                icon={<DeleteOutlined />}
              >
                Clear
              </Button>
            </div>
          </Card>
        </Col>
      ))}
    </Row>
  );

  /**
   * Renderiza controles
   */
  const renderControls = () => (
    <Space>
      <Button
        icon={<ReloadOutlined />}
        onClick={updateStats}
      >
        Refresh
      </Button>
      <Button
        icon={<ClearOutlined />}
        onClick={handleCleanup}
      >
        Cleanup
      </Button>
      <Button
        danger
        icon={<DeleteOutlined />}
        onClick={() => handleClearCache('all')}
      >
        Clear All
      </Button>
    </Space>
  );

  if (compact) {
    return (
      <Card size="small" title="Cache Status" extra={renderControls()}>
        {renderSummary()}
      </Card>
    );
  }

  return (
    <div>
      <Card title="Cache Monitor" extra={renderControls()}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {renderSummary()}
          {renderBreakdown()}
          
          {showDetails && (
            <Card title="Cache Details" size="small">
              <Tabs activeKey={selectedCache} onChange={setSelectedCache}>
                {Object.entries(stats).map(([type, data]) => (
                  <TabPane tab={`${type} (${data.size})`} key={type}>
                    <Table
                      dataSource={data.entries}
                      columns={columns}
                      size="small"
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true
                      }}
                      rowKey="key"
                    />
                  </TabPane>
                ))}
              </Tabs>
            </Card>
          )}
        </Space>
      </Card>
    </div>
  );
};

/**
 * Componente compacto para exibição em headers
 */
export const CacheStatus = () => {
  const { totalEntries, breakdown } = useCacheStats();
  const { isOnline } = useOfflineCache();

  return (
    <Tooltip title={`Cache: ${totalEntries} entries | ${isOnline ? 'Online' : 'Offline'}`}>
      <Space>
        <Tag color={isOnline ? 'green' : 'red'}>
          {isOnline ? <WifiOutlined /> : <DisconnectOutlined />}
        </Tag>
        <Tag color="blue">{totalEntries}</Tag>
      </Space>
    </Tooltip>
  );
};

export default CacheMonitor;
