/**
 * Utilitário para diagnóstico de APIs
 * Testa conectividade e configuração das APIs principais
 */

import axios from 'axios';

export const testApiConnectivity = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      stage: process.env.REACT_APP_STAGE,
      nodeEnv: process.env.NODE_ENV,
      apiPermission: process.env.REACT_APP_API_PERMISSION,
      cognitoParse: process.env.REACT_APP_COGNITO_PARSE,
    },
    tests: []
  };



  // Teste 1: API Permission - Health Check (sem withCredentials)
  try {
    console.log('🧪 Testando API Permission...');
    const response = await axios.get(`${process.env.REACT_APP_API_PERMISSION}health`, {
      timeout: 10000,
      withCredentials: false // Explicitamente desabilitar para evitar CORS
    });

    results.tests.push({
      name: 'API Permission Health',
      url: `${process.env.REACT_APP_API_PERMISSION}health`,
      status: 'success',
      statusCode: response.status,
      responseTime: response.headers['x-response-time'] || 'N/A'
    });
    console.log('✅ API Permission: OK');
  } catch (error) {
    results.tests.push({
      name: 'API Permission Health',
      url: `${process.env.REACT_APP_API_PERMISSION}health`,
      status: 'error',
      error: error.message,
      statusCode: error.response?.status || 'N/A'
    });

  }

  // Teste 2: Cognito Parse API (sem withCredentials)
  try {

    const response = await axios.get(`${process.env.REACT_APP_COGNITO_PARSE}`, {
      timeout: 10000,
      withCredentials: false // Explicitamente desabilitar para evitar CORS
    });

    results.tests.push({
      name: 'Cognito Parse API',
      url: process.env.REACT_APP_COGNITO_PARSE,
      status: 'success',
      statusCode: response.status,
      responseTime: response.headers['x-response-time'] || 'N/A'
    });

  } catch (error) {
    results.tests.push({
      name: 'Cognito Parse API',
      url: process.env.REACT_APP_COGNITO_PARSE,
      status: 'error',
      error: error.message,
      statusCode: error.response?.status || 'N/A'
    });

  }

  // Teste 3: Verificar conectividade básica da API (sem credentials)
  try {

    const response = await axios.get(`${process.env.REACT_APP_API_PERMISSION}`, {
      withCredentials: false,
      timeout: 10000
    });

    results.tests.push({
      name: 'API Basic Connectivity',
      url: `${process.env.REACT_APP_API_PERMISSION}`,
      status: 'success',
      statusCode: response.status,
      message: 'API respondendo normalmente'
    });

  } catch (error) {
    results.tests.push({
      name: 'API Basic Connectivity',
      url: `${process.env.REACT_APP_API_PERMISSION}`,
      status: 'error',
      error: error.message,
      statusCode: error.response?.status || 'N/A',
      message: 'API não está respondendo'
    });
    // API Basic Connectivity error
  }

  return results;
};

export const testMFAFlow = async (code) => {


  try {
    // Testar chamada real para Cognito
    const cognitoResponse = await axios.post(
      process.env.REACT_APP_COGNITO_PARSE,
      { code },
      {
        withCredentials: false,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );


    return {
      success: true,
      cognitoStatus: cognitoResponse.status,
      data: cognitoResponse.data,
      message: 'Fluxo MFA testado com sucesso'
    };
  } catch (error) {

    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
      message: 'Falha no teste do fluxo MFA'
    };
  }
};

export const testBackendConnectivity = async () => {


  const baseURL = process.env.REACT_APP_API_PERMISSION.replace(/\/$/, '');

  const tests = [
    { name: 'Root Endpoint', url: baseURL },
    { name: 'Health Check', url: `${baseURL}/health` },
    { name: 'Status Check', url: `${baseURL}/status` }
  ];

  const results = [];

  for (const test of tests) {
    try {

      const response = await axios.get(test.url, {
        withCredentials: false,
        timeout: 5000
      });

      results.push({
        ...test,
        status: 'success',
        statusCode: response.status,
        message: 'Endpoint respondendo'
      });

    } catch (error) {
      results.push({
        ...test,
        status: 'error',
        statusCode: error.response?.status || 'N/A',
        error: error.message,
        message: 'Endpoint não está respondendo'
      });

    }
  }

  return results;
};

export default {
  testApiConnectivity,
  testMFAFlow,
  testBackendConnectivity
};
