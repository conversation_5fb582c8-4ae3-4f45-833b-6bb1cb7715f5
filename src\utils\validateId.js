/**
 * Utilitário para validação robusta de IDs
 * Previne requisições com IDs inválidos que causam erros 502/404
 */

/**
 * Valida se um ID é válido para uso em requisições
 * @param {any} id - ID a ser validado
 * @param {string} context - Contexto da validação (para logs)
 * @returns {boolean} - true se o ID é válido
 */
export function isValidId(id, context = 'unknown') {
  const isValid = id !== null && 
                  id !== undefined && 
                  id !== 'null' && 
                  id !== 'undefined' && 
                  String(id).trim() !== '' &&
                  String(id).trim() !== 'NaN';

  if (!isValid) {
    console.warn(`⚠️ ${context}: ID inválido detectado`, { id, type: typeof id });
  }

  return isValid;
}

/**
 * Valida um ID e lança erro se inválido
 * @param {any} id - ID a ser validado
 * @param {string} context - Contexto da validação
 * @param {string} entityName - Nome da entidade (cliente, contrato, etc.)
 * @throws {Error} - Se o ID for inválido
 */
export function validateIdOrThrow(id, context = 'unknown', entityName = 'item') {
  if (!isValidId(id, context)) {
    const errorMessage = `ID do ${entityName} inválido: ${id}. Deve ser uma string não vazia ou número válido.`;
    console.error(`❌ ${context}: ${errorMessage}`);
    throw new Error(errorMessage);
  }
}

/**
 * Valida um ID e retorna um valor padrão se inválido
 * @param {any} id - ID a ser validado
 * @param {any} defaultValue - Valor padrão a retornar se ID for inválido
 * @param {string} context - Contexto da validação
 * @returns {any} - ID válido ou valor padrão
 */
export function validateIdOrDefault(id, defaultValue = null, context = 'unknown') {
  if (!isValidId(id, context)) {
    console.warn(`⚠️ ${context}: Usando valor padrão para ID inválido`, { id, defaultValue });
    return defaultValue;
  }
  return id;
}

/**
 * Valida múltiplos IDs de uma vez
 * @param {Object} ids - Objeto com IDs a serem validados
 * @param {string} context - Contexto da validação
 * @returns {Object} - Objeto com resultado da validação
 */
export function validateMultipleIds(ids, context = 'unknown') {
  const results = {};
  const errors = [];

  for (const [key, id] of Object.entries(ids)) {
    if (isValidId(id, `${context}.${key}`)) {
      results[key] = { valid: true, id };
    } else {
      results[key] = { valid: false, id, error: `ID inválido: ${id}` };
      errors.push(`${key}: ${id}`);
    }
  }

  if (errors.length > 0) {
    console.warn(`⚠️ ${context}: IDs inválidos encontrados`, { errors });
  }

  return {
    results,
    hasErrors: errors.length > 0,
    errors,
    validIds: Object.entries(results)
      .filter(([, result]) => result.valid)
      .reduce((acc, [key, result]) => ({ ...acc, [key]: result.id }), {})
  };
}

/**
 * Wrapper para localStorage.getItem com validação
 * @param {string} key - Chave do localStorage
 * @param {string} context - Contexto da validação
 * @returns {string|null} - Valor válido ou null
 */
export function getValidLocalStorageItem(key, context = 'localStorage') {
  const value = localStorage.getItem(key);
  
  if (!isValidId(value, `${context}.${key}`)) {
    console.warn(`⚠️ ${context}: Valor inválido no localStorage`, { key, value });
    return null;
  }
  
  return value;
}

/**
 * Cria um hook personalizado para validação de IDs em componentes React
 * @param {any} id - ID a ser validado
 * @param {string} context - Contexto da validação
 * @returns {Object} - Estado da validação
 */
export function useIdValidation(id, context = 'component') {
  const isValid = isValidId(id, context);
  
  return {
    isValid,
    id: isValid ? id : null,
    error: isValid ? null : `ID inválido: ${id}`,
    canMakeRequest: isValid
  };
}
