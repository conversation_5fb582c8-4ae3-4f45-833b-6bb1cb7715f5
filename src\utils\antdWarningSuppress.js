/**
 * Supressão específica para warnings do Antd relacionados ao findDOMNode
 * Esta é uma solução temporária até que o Antd seja atualizado para React 18
 */

// Interceptar warnings específicos do React relacionados ao Antd
const suppressAntdFindDOMNodeWarnings = () => {
  // Armazenar console original
  const originalError = console.error;
  
  console.error = function(...args) {
    // Converter argumentos para string
    const message = args.join(' ');
    
    // Lista de padrões específicos do warning do findDOMNode no Antd
    const antdFindDOMNodePatterns = [
      'Warning: findDOMNode is deprecated and will be removed in the next major release',
      'Instead, add a ref directly to the element you want to reference',
      'Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node',
      'at SingleObserver',
      'at ResizeObserver',
      'at Tooltip',
      'at MenuItem',
      'findDOMNode is deprecated',
      'findDOMNode was passed an instance',
      'which is inside StrictMode',
      'add a ref directly to the element you want to reference'
    ];
    
    // Verificar se a mensagem contém algum dos padrões do Antd
    const shouldSuppress = antdFindDOMNodePatterns.some(pattern => 
      message.includes(pattern)
    );
    
    // Se não deve ser suprimido, chamar o console.error original
    if (!shouldSuppress) {
      originalError.apply(console, args);
    }
    // Caso contrário, suprimir silenciosamente
  };
};

// Aplicar supressão imediatamente
suppressAntdFindDOMNodeWarnings();

// Exportar para uso manual se necessário
export { suppressAntdFindDOMNodeWarnings };

// Log apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  console.log('🔇 Antd findDOMNode warnings suppressed');
}
