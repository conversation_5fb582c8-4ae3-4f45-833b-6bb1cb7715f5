import React, { useState, useEffect } from 'react';
import { Button, Modal, Typography, List, Card, Space, Tag } from 'antd';
import { BugOutlined, ClearOutlined, CopyOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

const ErrorLogger = () => {
  const [visible, setVisible] = useState(false);
  const [errors, setErrors] = useState([]);

  useEffect(() => {
    // Load errors from sessionStorage
    const loadErrors = () => {
      const storedErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
      setErrors(storedErrors);
    };

    loadErrors();

    // Check for new errors every second
    const interval = setInterval(loadErrors, 1000);

    return () => clearInterval(interval);
  }, []);

  const clearErrors = () => {
    sessionStorage.removeItem('errorLog');
    setErrors([]);
  };

  const copyErrorsToClipboard = () => {
    const errorText = errors.map(error => {
      return `[${error.timestamp}] ${error.type || 'Error'}: ${error.message || error.reason}
Stack: ${error.stack || 'No stack trace'}
${error.filename ? `File: ${error.filename}:${error.line}:${error.column}` : ''}
---`;
    }).join('\n\n');

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('Errors copied to clipboard!');
    });
  };

  if (errors.length === 0) {
    return null;
  }

  return (
    <>
      {/* Floating debug button */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 9999,
        backgroundColor: '#ff4d4f',
        borderRadius: '50%',
        width: '60px',
        height: '60px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        animation: 'pulse 2s infinite'
      }} onClick={() => setVisible(true)}>
        <BugOutlined style={{ color: 'white', fontSize: '24px' }} />
        <div style={{
          position: 'absolute',
          top: '-8px',
          right: '-8px',
          backgroundColor: '#fff',
          color: '#ff4d4f',
          borderRadius: '50%',
          width: '24px',
          height: '24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold'
        }}>
          {errors.length}
        </div>
      </div>

      {/* Error modal */}
      <Modal
        title={
          <Space>
            <BugOutlined style={{ color: '#ff4d4f' }} />
            <span>Errors Captured ({errors.length})</span>
          </Space>
        }
        open={visible}
        onCancel={() => setVisible(false)}
        width={800}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={copyErrorsToClipboard}>
            Copy All Errors
          </Button>,
          <Button key="clear" icon={<ClearOutlined />} onClick={clearErrors}>
            Clear Errors
          </Button>,
          <Button key="close" type="primary" onClick={() => setVisible(false)}>
            Close
          </Button>
        ]}
      >
        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
          <List
            dataSource={errors}
            renderItem={(error, index) => (
              <List.Item key={index}>
                <Card size="small" style={{ width: '100%' }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Tag color={error.type === 'unhandledrejection' ? 'orange' : 'red'}>
                        {error.type || 'Error'}
                      </Tag>
                      <Text type="secondary">{error.timestamp}</Text>
                    </div>
                    
                    <Paragraph copyable strong>
                      {error.message || error.reason}
                    </Paragraph>
                    
                    {error.filename && (
                      <Text type="secondary">
                        📁 {error.filename}:{error.line}:{error.column}
                      </Text>
                    )}
                    
                    {error.stack && (
                      <details>
                        <summary style={{ cursor: 'pointer', color: '#00B050' }}>
                          📋 Stack Trace
                        </summary>
                        <pre style={{ 
                          fontSize: '12px', 
                          backgroundColor: '#f5f5f5', 
                          padding: '8px', 
                          borderRadius: '4px',
                          marginTop: '8px',
                          overflow: 'auto'
                        }}>
                          {error.stack}
                        </pre>
                      </details>
                    )}
                  </Space>
                </Card>
              </List.Item>
            )}
          />
        </div>
      </Modal>

      <style>{`
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
          }
        }
      `}</style>
    </>
  );
};

export default ErrorLogger;
