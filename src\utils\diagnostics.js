/**
 * Utilitários de diagnóstico para identificar problemas de autenticação
 */

export const runDiagnostics = () => {
  console.group("🔍 DIAGNÓSTICO COMPLETO DO SISTEMA");
  
  // 1. Verificar localStorage
  console.group("📦 localStorage");
  const localStorageData = {
    mail: localStorage.getItem("@dsm/mail"),
    name: localStorage.getItem("@dsm/name"),
    username: localStorage.getItem("@dsm/username"),
    permission: localStorage.getItem("@dsm/permission"),
    time: localStorage.getItem("@dsm/time"),
    jwt: localStorage.getItem("jwt") ? "Presente" : "Ausente",
    version: localStorage.getItem("version")
  };
  console.table(localStorageData);
  console.groupEnd();

  // 2. Verificar sessionStorage
  console.group("🗂️ sessionStorage");
  const sessionStorageData = {};
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    sessionStorageData[key] = sessionStorage.getItem(key);
  }
  console.table(sessionStorageData);
  console.groupEnd();

  // 3. Verificar cookies
  console.group("🍪 Cookies");
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    if (key) acc[key] = value || 'empty';
    return acc;
  }, {});
  console.table(cookies);
  console.groupEnd();

  // 4. Verificar variáveis de ambiente
  console.group("🌍 Variáveis de Ambiente");
  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    REACT_APP_STAGE: process.env.REACT_APP_STAGE,
    REACT_APP_API_PERMISSION: process.env.REACT_APP_API_PERMISSION,
    REACT_APP_COGNITO_PARSE: process.env.REACT_APP_COGNITO_PARSE,
    REACT_APP_USER_POOL_ID: process.env.REACT_APP_USER_POOL_ID
  };
  console.table(envVars);
  console.groupEnd();

  // 5. Verificar URL atual
  console.group("🌐 Informações da URL");
  const urlInfo = {
    href: window.location.href,
    pathname: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash,
    origin: window.location.origin
  };
  console.table(urlInfo);
  console.groupEnd();

  // 6. Teste de conectividade com APIs
  console.group("🔗 Teste de Conectividade");
  testAPIConnectivity();
  console.groupEnd();

  console.groupEnd();
};

const testAPIConnectivity = async () => {
  const apis = [
    {
      name: "API Permission",
      url: process.env.REACT_APP_API_PERMISSION,
      endpoints: ["health", "cognito/read"],
      requiresAuth: true
    },
    {
      name: "Cognito Parse",
      url: process.env.REACT_APP_COGNITO_PARSE,
      endpoints: [""],
      requiresAuth: false
    },
    {
      name: "API Dynamo",
      url: process.env.REACT_APP_API_DYNAMO,
      endpoints: ["health", `${process.env.REACT_APP_STAGE}-permissions`],
      requiresAuth: true
    }
  ];

  const jwt = localStorage.getItem("jwt");

  for (const api of apis) {
    console.group(`🔗 Testando ${api.name}`);

    for (const endpoint of api.endpoints) {
      try {
        const testUrl = `${api.url}${endpoint}`;
        console.log(`URL: ${testUrl}`);

        const headers = {
          'Content-Type': 'application/json'
        };

        if (api.requiresAuth && jwt) {
          headers['Authorization'] = `Bearer ${jwt}`;
          headers['authorization'] = jwt; // Formato alternativo
        }

        const response = await fetch(testUrl, {
          method: 'GET',
          mode: 'cors',
          headers: headers,
          timeout: 10000
        });

        const status = response.status;
        const statusText = response.statusText;

        if (status >= 200 && status < 300) {
          console.log(`✅ ${endpoint || 'root'}: ${status} ${statusText}`);

          // Tentar ler resposta se for JSON
          try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              const data = await response.json();
              console.log(`📄 Resposta:`, data);
            }
          } catch (e) {
            console.log(`📄 Resposta não é JSON válido`);
          }
        } else {
          console.log(`⚠️ ${endpoint || 'root'}: ${status} ${statusText}`);

          if (status === 401) {
            console.log(`🔐 Erro de autenticação - Token pode estar inválido`);
          } else if (status === 403) {
            console.log(`🚫 Erro de autorização - Sem permissão`);
          } else if (status === 404) {
            console.log(`🔍 Endpoint não encontrado`);
          } else if (status >= 500) {
            console.log(`💥 Erro interno do servidor`);
          }
        }

      } catch (error) {
        console.log(`❌ ${endpoint || 'root'}: ${error.message}`);

        if (error.message.includes('CORS')) {
          console.log(`🌐 Problema de CORS - Backend pode não estar configurado`);
        } else if (error.message.includes('Failed to fetch')) {
          console.log(`📡 Falha na conexão - Backend pode estar offline`);
        } else if (error.message.includes('timeout')) {
          console.log(`⏱️ Timeout - Backend muito lento ou indisponível`);
        }
      }
    }

    console.groupEnd();
  }
};

// Função para testar autenticação
export const testAuthentication = async () => {
  console.group("🔐 TESTE DE AUTENTICAÇÃO");
  
  const jwt = localStorage.getItem("jwt");
  if (!jwt) {
    console.log("❌ Nenhum token JWT encontrado");
    console.groupEnd();
    return false;
  }

  try {
    // Importar dinamicamente para evitar problemas de dependência
    const { jwtDecode } = await import("jwt-decode");
    const decoded = jwtDecode(jwt);
    
    console.log("📋 Token decodificado:");
    console.table({
      email: decoded.email,
      exp: new Date(decoded.exp * 1000).toLocaleString(),
      iat: new Date(decoded.iat * 1000).toLocaleString(),
      expired: decoded.exp * 1000 < Date.now()
    });

    if (decoded.exp * 1000 < Date.now()) {
      console.log("❌ Token expirado");
      console.groupEnd();
      return false;
    }

    console.log("✅ Token válido");
    console.groupEnd();
    return true;
  } catch (error) {
    console.log("❌ Erro ao decodificar token:", error.message);
    console.groupEnd();
    return false;
  }
};

// Função para limpar dados corrompidos
export const cleanupCorruptedData = () => {
  console.group("🧹 LIMPEZA DE DADOS");
  
  // Limpar localStorage
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    
    // Verificar se o valor está corrompido
    if (value === 'undefined' || value === 'null' || value === '') {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    console.log(`🗑️ Removido: ${key}`);
  });
  
  console.log(`✅ Limpeza concluída. ${keysToRemove.length} itens removidos.`);
  console.groupEnd();
};

// Função para testar especificamente o backend
export const testBackendHealth = async () => {
  console.group("🏥 TESTE DE SAÚDE DO BACKEND");

  const jwt = localStorage.getItem("jwt");
  const permission = localStorage.getItem("@dsm/permission");

  console.log("Token JWT:", jwt ? "Presente" : "Ausente");
  console.log("Permissão:", permission);

  // Teste 1: API de Permissões (crítica para Home)
  console.group("1️⃣ Teste API de Permissões");
  try {
    const permissionUrl = `${process.env.REACT_APP_API_DYNAMO}${process.env.REACT_APP_STAGE}-permissions/${permission}`;
    console.log("URL:", permissionUrl);

    const response = await fetch(permissionUrl, {
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log("✅ API de Permissões funcionando");
      console.log("Permissões encontradas:", data.permissions?.length || 0);

      const homePermissions = data.permissions?.find(p => p.page === "Home");
      console.log("Permissões da Home:", homePermissions ? "Encontradas" : "Não encontradas");
    } else {
      console.log(`❌ API de Permissões falhou: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Erro na API de Permissões: ${error.message}`);
  }
  console.groupEnd();

  // Teste 2: API de Switch Roles (usada na Home)
  console.group("2️⃣ Teste API Switch Roles");
  try {
    const switchRolesUrl = `${process.env.REACT_APP_API_PERMISSION}switch-roles`;
    console.log("URL:", switchRolesUrl);

    const response = await fetch(switchRolesUrl, {
      headers: {
        'authorization': jwt,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      console.log("✅ API Switch Roles funcionando");
    } else {
      console.log(`❌ API Switch Roles falhou: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Erro na API Switch Roles: ${error.message}`);
  }
  console.groupEnd();

  console.groupEnd();
};

// Função para diagnosticar problema do invalid_grant
export const diagnoseInvalidGrant = async () => {
  console.group("🔍 DIAGNÓSTICO INVALID_GRANT");

  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');

  console.log("Código na URL:", code);
  console.log("URL completa:", window.location.href);

  if (!code) {
    console.log("❌ Nenhum código encontrado na URL");
    console.groupEnd();
    return;
  }

  // Testar o endpoint Cognito Parse
  try {
    console.log("🧪 Testando endpoint Cognito Parse...");
    const response = await fetch(process.env.REACT_APP_COGNITO_PARSE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: code })
    });

    const data = await response.json();

    console.log("📊 Resposta do Cognito Parse:");
    console.log("- Status:", response.status);
    console.log("- Status Code:", data.statusCode);
    console.log("- Erro:", data.data?.error);
    console.log("- Token presente:", !!data.data?.id_token);

    if (data.data?.error === 'invalid_grant') {
      console.group("⚠️ ANÁLISE DO INVALID_GRANT");
      console.log("Possíveis causas:");
      console.log("1. Código já foi usado anteriormente");
      console.log("2. Código expirou (geralmente 10 minutos)");
      console.log("3. Código inválido ou corrompido");
      console.log("4. Problema de sincronização de tempo");

      if (data.data?.id_token) {
        console.log("✅ Apesar do erro, token JWT foi retornado");
        console.log("🔧 Sistema pode continuar funcionando");
      } else {
        console.log("❌ Nenhum token retornado, erro crítico");
      }
      console.groupEnd();
    }

  } catch (error) {
    console.error("❌ Erro ao testar Cognito Parse:", error);
  }

  console.groupEnd();
};

// Função para diagnosticar problemas de CORS/HttpOnly no backend
export const diagnoseCorsHttpOnly = async () => {
  console.group("🌐 DIAGNÓSTICO CORS/HTTPONLY NO BACKEND");

  const jwt = localStorage.getItem("jwt");
  const permission = localStorage.getItem("@dsm/permission");

  console.log("🔍 Dados locais:");
  console.log("- JWT presente:", !!jwt);
  console.log("- Permissão:", permission);

  // Teste simples sem credentials primeiro
  console.group("🧪 TESTE SIMPLES SEM CREDENTIALS");
  try {
    const testUrl = `${process.env.REACT_APP_API_DYNAMO}health`;
    console.log("Testando URL simples:", testUrl);

    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
      // SEM credentials: 'include'
    });

    console.log("✅ Teste simples funcionou:", response.status);
  } catch (error) {
    console.log("❌ Teste simples falhou:", error.message);
  }
  console.groupEnd();

  // Teste 1: Verificar se RequireAuth está funcionando
  console.group("1️⃣ Teste RequireAuth");
  try {
    // Simular o que RequireAuth faz
    const authResult = jwt && permission;
    console.log("RequireAuth deveria passar:", authResult);

    if (!authResult) {
      console.log("❌ RequireAuth vai falhar - dados ausentes");
    } else {
      console.log("✅ RequireAuth deveria funcionar");
    }
  } catch (error) {
    console.log("❌ Erro no RequireAuth:", error);
  }
  console.groupEnd();

  // Teste 2: APIs críticas do Dashboard (SEM CORS/HttpOnly)
  console.group("2️⃣ Teste APIs do Dashboard - SEM CORS");

  const criticalAPIs = [
    {
      name: "Permissões (DynamoDB)",
      url: `${process.env.REACT_APP_API_DYNAMO}${process.env.REACT_APP_STAGE}-permissions/${permission}`,
      headers: { 'Authorization': `Bearer ${jwt}` }
    },
    {
      name: "Switch Roles",
      url: `${process.env.REACT_APP_API_PERMISSION}switch-roles`,
      headers: { 'authorization': jwt }
    },
    {
      name: "User Data",
      url: `${process.env.REACT_APP_API_PERMISSION}user`,
      headers: { 'authorization': jwt }
    }
  ];

  for (const api of criticalAPIs) {
    try {
      console.log(`🧪 Testando ${api.name}...`);
      console.log(`URL: ${api.url}`);
      console.log(`Headers:`, api.headers);

      // Teste SEM credentials (backend sem CORS/HttpOnly)
      const response = await fetch(api.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...api.headers
        }
        // SEM credentials: 'include' - backend não precisa mais
      });

      const status = response.status;

      if (status === 200) {
        console.log(`✅ ${api.name}: OK`);
      } else if (status === 401) {
        console.log(`🔐 ${api.name}: 401 Unauthorized - Token inválido ou HttpOnly ativo`);
      } else if (status === 403) {
        console.log(`🚫 ${api.name}: 403 Forbidden - Sem permissão`);
      } else if (status === 404) {
        console.log(`🔍 ${api.name}: 404 Not Found - Endpoint não existe`);
      } else {
        console.log(`⚠️ ${api.name}: ${status} - Erro no servidor`);
      }

    } catch (error) {
      if (error.message.includes('CORS')) {
        console.log(`🌐 ${api.name}: ERRO DE CORS - Backend não configurado para localhost:3000`);
      } else if (error.message.includes('Failed to fetch')) {
        console.log(`📡 ${api.name}: FALHA DE CONEXÃO - Backend offline ou bloqueado`);
      } else {
        console.log(`❌ ${api.name}: ${error.message}`);
      }
    }
  }
  console.groupEnd();

  // Teste 3: Verificar configuração de cookies
  console.group("3️⃣ Teste Configuração de Cookies");
  const cookies = document.cookie;
  console.log("Cookies atuais:", cookies || "Nenhum cookie encontrado");

  if (cookies.includes('auth-token') || cookies.includes('jwt')) {
    console.log("🍪 Cookies de autenticação encontrados - Sistema HttpOnly pode estar ativo");
  } else {
    console.log("📭 Nenhum cookie de autenticação - Sistema localStorage funcionando");
  }
  console.groupEnd();

  // Teste 4: Verificar se CORS foi realmente removido
  console.group("4️⃣ Verificação de Remoção de CORS");

  try {
    const testUrl = `${process.env.REACT_APP_API_DYNAMO}${process.env.REACT_APP_STAGE}-permissions/${permission}`;
    console.log("🔍 Testando remoção de CORS...");
    console.log("URL:", testUrl);

    // Fazer requisição OPTIONS (preflight)
    const optionsResponse = await fetch(testUrl, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Authorization, Content-Type'
      }
    });

    console.log("Resposta OPTIONS:", optionsResponse.status);
    console.log("Headers da resposta:");

    const corsHeaders = {
      'access-control-allow-origin': optionsResponse.headers.get('access-control-allow-origin'),
      'access-control-allow-credentials': optionsResponse.headers.get('access-control-allow-credentials'),
      'access-control-allow-headers': optionsResponse.headers.get('access-control-allow-headers'),
      'access-control-allow-methods': optionsResponse.headers.get('access-control-allow-methods')
    };

    console.log(corsHeaders);

    if (!corsHeaders['access-control-allow-origin']) {
      console.log("✅ CORS removido com sucesso - nenhum header CORS presente");
    } else {
      console.log("⚠️ CORS ainda presente:", corsHeaders['access-control-allow-origin']);
    }

  } catch (error) {
    console.log("❌ Erro ao testar CORS:", error.message);
  }
  console.groupEnd();

  console.group("📋 RESUMO DO DIAGNÓSTICO");
  console.log("Se você vê:");
  console.log("- ❌ CORS errors → Backend ainda tem CORS (não foi removido)");
  console.log("- 🔐 401 Unauthorized → Backend ainda espera HttpOnly cookies");
  console.log("- 📡 Failed to fetch → Backend offline ou bloqueado");
  console.log("- ✅ 200 OK → Backend funcionando sem CORS/HttpOnly");
  console.log("- ⚠️ Outros erros → Problema específico da API");
  console.groupEnd();

  console.groupEnd();
};

// Adicionar funções globais para debug
if (typeof window !== 'undefined') {
  window.runDiagnostics = runDiagnostics;
  window.testAuthentication = testAuthentication;
  window.cleanupCorruptedData = cleanupCorruptedData;
  window.testBackendHealth = testBackendHealth;
  window.diagnoseInvalidGrant = diagnoseInvalidGrant;
  window.diagnoseCorsHttpOnly = diagnoseCorsHttpOnly;

  console.log("🛠️ Funções de diagnóstico disponíveis:");
  console.log("  - runDiagnostics() - Diagnóstico completo");
  console.log("  - testAuthentication() - Testar autenticação");
  console.log("  - cleanupCorruptedData() - Limpar dados corrompidos");
  console.log("  - testBackendHealth() - Testar saúde do backend");
  console.log("  - diagnoseInvalidGrant() - Analisar erro invalid_grant");
  console.log("  - diagnoseCorsHttpOnly() - Diagnosticar CORS/HttpOnly no backend");
}
