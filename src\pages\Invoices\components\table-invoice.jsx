import React, { useEffect, useMemo, useState } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Row, Col, Table, Tooltip } from "antd";
import { Counter } from "../../../components/Counter";
import { LoadingOutlined } from "@ant-design/icons";
import * as controller from "../../../controllers/invoices/invoiceController";
import { INVOICE_VIEW_STATES } from "../../../store/reducers/invoices-reducer";
import { moneyMask } from "../../../utils/money-maks";

export const InvoiceTable = () => {
  const view = useSelector((state) => state.invoice.view, shallowEqual);
  const search = useSelector((state) => state.invoice.search, shallowEqual);
  const loading = useSelector((state) => state.invoice.loading, shallowEqual);
  const invoices = useSelector((state) => state.invoice.invoices, shallowEqual);
  const dolar = useSelector((state) => state.invoice.dolar, shallowEqual);
  const permissions = useSelector(
    (state) => state.invoice.permissions,
    shallowEqual
  );
  const collumnsToShow = useSelector(
    (state) => state.invoice.collumnsToShow,
    shallowEqual
  );
  const [invoiceColumns, setInvoiceColumns] = useState([]);

  const invoiceList = useMemo(() => {
    let list = invoices;

    if (view === INVOICE_VIEW_STATES.SUMMARY) {
      list = controller.groupInvoiceByInvoiceId(list, dolar);
    } else {
      list = controller.groupInvoiceByAccount(list, dolar);
    }

    return list;
  }, [invoices, view]);

  const invoicesFiltered = useMemo(() => {
    let filteredData = invoiceList;

    if (search !== "")
      filteredData = controller.filterBySearch(filteredData, search);

    return filteredData;
  }, [search, invoiceList]);

  let columns = [
    {
      code: "view_bill_payer_account",
      title: "Bill Payer Account",
      dataIndex: "bill_payer_account_id",
      key: "bill_payer_account_id",
      sorter: (a, b) => a?.bill_payer_account_id - b?.bill_payer_account_id,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_account_id",
      title: "Account ID",
      dataIndex: "line_item_usage_account_id",
      key: "line_item_usage_account_id",
      defaultSortOrder: "descend",
      sorter: (a, b) =>
        a?.line_item_usage_account_id - b?.line_item_usage_account_id,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_customer",
      title: "Customer",
      dataIndex: "customer",
      key: "customer",
      sorter: (a, b) => b?.customer?.localeCompare(a?.customer),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return !item.customer ? (
          <Row justify="center">
            <LoadingOutlined />
          </Row>
        ) : (
          <span>{item.customer === "false" ? "" : item.customer}</span>
        );
      },
    },
    {
      code: "view_billing_entity",
      title: "Billing Entity",
      dataIndex: "bill_billing_entity",
      key: "bill_billing_entity",
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_invoice_id",
      title: "Invoice ID",
      dataIndex: "bill_invoice_id",
      sorter: (a, b) => a?.bill_invoice_id - b?.bill_invoice_id,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_month",
      title: "Month",
      dataIndex: "month",
      key: "month",
      sorter: (a, b) => a?.month - b?.month,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_cost",
      title: "Charges (US$)",
      dataIndex: "charges",
      key: "charges",
      align: "center",
      sorter: (a, b) => a?.unblended_cost - b?.unblended_cost,
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Charges (US$)"}>
            {moneyMask(parseFloat(item.charges).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_spp_discount",
      title: "SPP Discount",
      dataIndex: "spp_discount",
      align: "center",
      sorter: (a, b) => parseFloat(a.spp_discount) - parseFloat(b.spp_discount),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"SPP Discount"}>
            {parseFloat(item.spp_discount).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.spp_discount).toFixed(2))}`
              : moneyMask(parseFloat(item.spp_discount).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_edp_discount",
      title: "EDP Discount",
      dataIndex: "total_edp_discount",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.total_edp_discount) - parseFloat(b.total_edp_discount),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"EDP Discount"}>
            {parseFloat(item.total_edp_discount).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.total_edp_discount).toFixed(2))}`
              : moneyMask(parseFloat(item.total_edp_discount).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_bundled_discount",
      title: "Bundled Discount",
      dataIndex: "total_bundled_discount",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.total_bundled_discount) -
        parseFloat(b.total_bundled_discount),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Bundled Discount"}>
            {parseFloat(item.total_bundled_discount).toFixed(2) === 0.0
              ? `${moneyMask(
                  parseFloat(item.total_bundled_discount).toFixed(2)
                )}`
              : moneyMask(parseFloat(item.total_bundled_discount).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_saving_plans_discount",
      title: "Saving Plans Discount",
      dataIndex: "total_saving_plans_discount",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.total_saving_plans_discount) -
        parseFloat(b.total_saving_plans_discount),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Saving Plans Discount"}>
            {parseFloat(item.total_saving_plans_discount).toFixed(2) === 0.0
              ? `${moneyMask(
                  parseFloat(item.total_saving_plans_discount).toFixed(2)
                )}`
              : moneyMask(
                  parseFloat(item.total_saving_plans_discount).toFixed(2)
                )}
          </Tooltip>
        );
      },
    },
    {
      code: "view_credits",
      title: "Credits",
      dataIndex: "total_credits",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.total_credits) - parseFloat(b.total_credits),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Credits"}>
            {parseFloat(item.total_credits).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.total_credits).toFixed(2))}`
              : moneyMask(parseFloat(item.total_credits).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_other_discounts",
      title: "Outros Descontos",
      dataIndex: "other_discounts",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.other_discounts) - parseFloat(b.other_discounts),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <span>{moneyMask(parseFloat(item.other_discounts).toFixed(2))}</span>
        );
      },
    },
    {
      code: "view_cost",
      title: "Tax (US$)",
      dataIndex: "tax",
      key: "tax",
      align: "center",
      sorter: (a, b) => a?.tax - b?.tax,
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Tax (US$)"}>
            {moneyMask(parseFloat(item.tax).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_cost",
      title: "Tax (%)",
      dataIndex: "tax_percentage",
      key: "tax_percentage",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a?.tax_percentage) - parseFloat(b?.tax_percentage),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Tax Percentage (%)"}>{item.tax_percentage}</Tooltip>
        );
      },
    },
    {
      code: "view_cost",
      title: "Total (US$)",
      dataIndex: "unblended_cost",
      key: "unblended_cost",
      align: "center",
      sorter: (a, b) => a?.unblended_cost - b?.unblended_cost,
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Total (Charges + Total Discount + Tax)"}>
            {moneyMask(parseFloat(item.unblended_cost).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_conversion",
      title: "Total (R$)",
      dataIndex: "conversion",
      render: (_, item) => {
        return (
          <Tooltip title={"Total (R$)"}>
            {parseFloat(item.conversion).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.conversion).toFixed(2))}`
              : moneyMask(parseFloat(item.conversion).toFixed(2))}
          </Tooltip>
        );
      },
      sorter: (a, b) => a?.conversion - b?.conversion,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_total_discount",
      title: "Total Discount",
      dataIndex: "total_discount",
      align: "center",
      sorter: (a, b) => parseFloat(a.spp_discount) - parseFloat(b.spp_discount),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip title={"Total Discount"}>
            {parseFloat(item.total_discount).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.total_discount).toFixed(2))}`
              : moneyMask(parseFloat(item.total_discount).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_line_item_legal_entity",
      title: "Line Item Legal Entity",
      dataIndex: "line_item_legal_entity",
      key: "line_item_legal_entity",
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_final_balance",
      title: "Final Balance (US$)",
      dataIndex: "final_balance",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.final_balance) - parseFloat(b.final_balance),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip
            title={"Final Balance (Total (US$) + SPP Discount + EDP Discount)"}
          >
            {parseFloat(item.final_balance).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.final_balance).toFixed(2))}`
              : moneyMask(parseFloat(item.final_balance).toFixed(2))}
          </Tooltip>
        );
      },
    },
    {
      code: "view_final_balance",
      title: "Final Balance (R$)",
      dataIndex: "final_balance_real",
      align: "center",
      sorter: (a, b) =>
        parseFloat(a.final_balance_real) - parseFloat(b.final_balance_real),
      sortDirections: ["descend", "ascend"],
      render: (_, item) => {
        return (
          <Tooltip
            title={"Final Balance (Total (US$) + SPP Discount + EDP Discount)"}
          >
            {parseFloat(item.final_balance_real).toFixed(2) === 0.0
              ? `${moneyMask(parseFloat(item.final_balance_real).toFixed(2))}`
              : moneyMask(parseFloat(item.final_balance_real).toFixed(2))}
          </Tooltip>
        );
      },
    },
  ];

  useEffect(() => {
    columns = columns.filter((column) => collumnsToShow.includes(column.title));
    setInvoiceColumns(columns);
  }, [collumnsToShow]);

  return (
    <>
      <Counter tableData={invoiceList} />
      <Row align="middle" justify="center">
        <Col span={24}>
          <Table
            scroll={{ x: "100%" }}
            style={{ minWidth: "100%" }}
            dataSource={invoicesFiltered}
            loading={loading}
            columns={(() => {
              const permissionCodes = permissions?.map((permission) => permission.code) || [];
              const filteredColumns = invoiceColumns.filter((e) => permissionCodes.includes(e.code));

              console.log('🔍 Invoices: Filtro de colunas:', {
                totalColumns: invoiceColumns.length,
                permissions: permissionCodes,
                filteredColumns: filteredColumns.length,
                columnCodes: filteredColumns.map(c => c.code),
                invoicesData: invoicesFiltered?.length || 0
              });

              return filteredColumns;
            })()}
            pagination={{ data: [] }}
          />
        </Col>
      </Row>
    </>
  );
};
