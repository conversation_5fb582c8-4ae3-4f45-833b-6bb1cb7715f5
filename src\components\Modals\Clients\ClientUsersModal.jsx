import {
  Col,
  Row,
  Tag,
  Table,
  Input,
  Modal,
  Button,
  message,
  Typography,
  Popconfirm,
  Tooltip,
} from "antd";
import { useMemo, useState } from "react";
import { ContactEditModal } from "../Contacts/ContactEdit";
import { ContactCreateModal } from "../Contacts/ContactCreate";
import { UserOutlined, CopyOutlined } from "@ant-design/icons";

import { customerPortalNewProvider } from "../../../provider/customer-portal-new-provider";

import axios from "axios";
import { dynamoGetById, dynamoPost } from "../../../service/apiDsmDynamo";
import { getCustomerContracts } from "../../../controllers/clients/clientsController";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { CustomerPortalButton } from "./components/clientUsersModal/CustomerPortalButton";
import { ResetMFAButton } from "./components/clientUsersModal/ResetMFAButton";
import { ToggleActive } from "./components/clientUsersModal/ToggleActive";
import {
  formatOtrsUpdateBody,
  formatUpdateBody,
  getCurrentContacts,
  otrsUpdate,
  resetMfaController,
  updateDynamo,
} from "./controllers/clientUsersModal";

export const ClientUsersModal = (props) => {
  const [contacts, setContacts] = useState([]);
  const { clients, client, permissions, functions, state } = props;
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const [contracts, setContracts] = useState([]);
  const [selectedContact, setSelectedContact] = useState();
  const [search, setSearch] = useState("");
  const [ticket, setTicket] = useState("");
  const [mfaLoading, setMfaLoading] = useState(false);
  const { Text } = Typography;

  const handleRefreshContacts = async () => {
    setLoading(true);
    try {
      for (let i = 0; i < functions.length; i++) {
        if (state === functions[i].state) {
          await functions[i].function();
        } else if (state === "todos") {
          await functions[i].function();
        }
      }
    } catch (err) {
      console.log(err);
      message.error(
        "Ocorreu um erro ao tentar atualizar os contatos, tente novamente"
      );
    }
    setLoading(false);
  };

  const handleGetCurrentContacts = async () => {
    setLoading(true);
    try {
      const { contacts } = await getCurrentContacts(client);
      setContacts(contacts);
    } catch (err) {
      console.log(err);
      message.error(
        "Ocorreu um erro ao tentar atualizar os contatos, tente novamente"
      );
    }
    setLoading(false);
  };

  const changeActive = async (data) => {
    setLoading(true);
    const { contacts } = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-customers`,
      client.id
    );
    const clientWithUpdatedContacts = { ...client, contacts: contacts };
    const updateClient = formatUpdateBody(
      clientWithUpdatedContacts,
      data,
      true
    );
    const otrsUpdateBody = formatOtrsUpdateBody(
      clientWithUpdatedContacts,
      data
    );
    try {
      await otrsUpdate(data.identifications.itsm_id, otrsUpdateBody);
      try {
        await updateDynamo(client.id, updateClient, data, true);
        const username = localStorage.getItem("@dsm/username");
        const title = `Contato ${data.active !== 1 ? "ativado" : "desativado"}`;
        const description = `${username} ${
          data.active === 1 ? "desativou" : "ativou"
        } o contato ${data?.names?.first_name} ${
          data?.names?.last_name
        }, no cliente ${client?.names?.fantasy_name || client?.names?.name}.`;
        dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
          username: username,
          name: title,
          description: description,
          created_at: new Date(),
          updated_at: new Date(),
        });
      } catch (err) {
        console.log(err);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }

    await handleGetCurrentContacts();
  };

  const resetMfa = async (value) => {
    setMfaLoading(true);
    try {
      await resetMfaController(value, ticket);
      setMfaLoading(false);
    } catch (err) {
      message.error("Ocorreu um erro ao resetar o MFA");
      setTicket("");
      setMfaLoading(false);
    }
  };

  const token = process.env.REACT_APP_CUSTOMER_PORTAL_TOKEN;

  const customerPortal = async (item) => {
    const isValidRoleGroup = (group) => {
      return !group || !group.data || typeof group.data.id === "undefined";
    };

    if (!contracts?.length || !contracts) {
      setLoading(false);
      return message.error(
        "Nenhum contrato encontrado para este cliente. Acesso ao portal não pode ser liberado."
      );
    }

    try {
      const provider = customerPortalNewProvider();

      const groupResponse = await provider.get(
        `get/group?itsm_id=${client.identifications.itsm_id}`
      );

      var groupID = null;
      var roleID = null;

      const createRole = async (groupID, arrContracts) => {
        const role = await axios.post(
          `${process.env.REACT_APP_API_CUSTOMER_PORTAL}private/roles`,
          {
            group_id: groupID,
            name: "admin",
            permitted_contracts: arrContracts,
            permissions: [
              "tickets",
              "consume_hours",
              "tickets_panel",
              "access_management",
              "executives",
            ],
            permitted_companies: [client.identifications.itsm_id.toString()],
            can_edit: 0,
          },
          {
            headers: {
              Authorization: token,
            },
          }
        );
        return role.data.data.role.id;
      };

      if (isValidRoleGroup(groupResponse)) {
        console.log("Grupo não encontrado. Criando um novo...");
        const group = await provider.post("create/customer/group", {
          name: client.names.fantasy_name || client.names.name,
          itsm_id: client.identifications.itsm_id,
        });

        groupID = group?.data?.data?.group?.id;
        if (!groupID) {
          console.error("Erro: ID do grupo não foi retornado após a criação.");
          return message.error(
            "Erro ao criar grupo. Acesso ao portal não pode ser liberado."
          );
        }
      } else {
        groupID = groupResponse?.data?.id;
      }

      const apiBaseUrl = process.env.REACT_APP_API_CUSTOMER_PORTAL;
      const roleResponse = await axios.get(
        `${apiBaseUrl}private/roles/groupid/ajusted/${groupID}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (roleResponse?.data?.data?.length > 0) {
        roleID = roleResponse.data.data[0].id;
      } else {
        const arrContracts = contracts.map((contract) =>
          contract.identifications.itsm_id.toString()
        );

        if (!arrContracts.length) {
          return message.error(
            "Nenhum contrato ativo foi encontrado para esse cliente. Verifique se há contratos válidos!"
          );
        }

        roleID = await createRole(groupID, arrContracts);
      }

      await axios.post(
        `${process.env.REACT_APP_API_CUSTOMER_PORTAL}private/users`,
        {
          email: item.email,
          name: item.email.split("@")[0],
          group_id: groupID,
          role_id: roleID,
          otrs_customer_ids: client.identifications.itsm_id,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      const username = localStorage.getItem("@dsm/username");
      const title = "Acesso ao Área Logada liberado";
      const description = `${username} liberou o acesso a área logada para o usuário ${
        item.names.first_name
      } ${item.names.last_name}, do cliente ${
        client?.names?.fantasy_name || client?.names?.name
      }`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });

      message.success("Sucesso na liberação do acesso ao portal do cliente!");
    } catch (err) {
      console.error("Erro inesperado:", err);

      const isUserExistError =
        err.response?.data?.status === "error" &&
        err.response?.data?.data?.error === "user_exist";

      if (isUserExistError) {
        message.error(
          "O usuário já existe no sistema. Não é possível criar duplicado."
        );
      } else {
        message.error(
          "Ops! Ocorreu um erro inesperado na liberação de acesso ao portal do cliente..."
        );
      }
    }
  };

  const resetUserAccess = async (email) => {
    try {
      let { data } = await axios.post(
        process.env.REACT_APP_API_CUSTOMER_PORTAL + "private/users/bypass",
        {
          email,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (data.status === "success") {
        const username = localStorage.getItem("@dsm/username");
        const title = "Reset de Senha do Portal do Cliente";
        const description = `${username} resetou a senha do usuário ${email} do Portal do Cliente`;
        logNewAuditAction(username, title, description);

        message.success(
          "Sucesso no reset deste acesso ao portal do cliente! Solicite que o usuário faça o processo de 'Esqueci minha senha' no portal Área Logada.",
          10
        );
      } else {
        message.error(
          "Erro ao tentar resetar a senha deste usuário no portal Área Logada..."
        );
      }
    } catch (error) {
      message.error(
        "Erro ao tentar resetar a senha deste usuário no portal Área Logada..."
      );
    }
  };

  const handleOpenModal = async () => {
    console.log('🔍 ClientUsersModal: Abrindo modal de contatos', {
      clientId: client?.id,
      propsContacts: props?.contacts?.length || 0,
      clientContacts: client?.contacts?.length || 0
    });

    setContacts(props?.contacts);
    await handleGetCurrentContacts();
    setShowModal(true);
    let result = await getCustomerContracts(client?.id);
    setContracts(result);
  };

  const columns = [
    {
      code: "view_contact_dsm_id",
      title: "DSM ID",
      align: "center",
      dataIndex: "dsm_id",
      sorter: (a, b) => a.dsm_id?.localeCompare(b.dsm_id),
      render: (field, item) => item.dsm_id || "-",
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_contact_crm",
      title: "CRM",
      dataIndex: ["identifications", "crm_id"],
      defaultSortOrder: "descend",
      sorter: (a, b) => a?.identifications?.crm_id - b?.identifications?.crm_id,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_contact_itsm",
      title: "ITSM",
      dataIndex: ["identifications", "itsm_id"],
      defaultSortOrder: "descend",
      sorter: (a, b) =>
        a?.identifications?.itsm_id - b?.identifications?.itsm_id,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_contact_first_name",
      title: "Primeiro nome",
      dataIndex: ["names", "first_name"],
    },
    {
      code: "view_contact_last_name",
      title: "Último nome",
      dataIndex: ["names", "last_name"],
    },
    {
      code: "view_contact_phone",
      title: "Telefone",
      dataIndex: "phone",
    },
    {
      code: "view_contact_email",
      title: "Email",
      dataIndex: "email",
      width: "1",
      render: (field) => {
        return (
          <Row>
            <Text>{field}</Text>
            <CopyOutlined
              style={{
                marginLeft: 10,
                marginTop: 5,
              }}
              onClick={() => {
                message.info("E-mail copiado com sucesso!");
                navigator.clipboard.writeText(field);
              }}
            />
          </Row>
        );
      },
    },
    {
      code: "view_contact_type",
      title: "Tipo",
      align: "center",
      dataIndex: "contact_type",
      render: (_, item) => item.contact_type || "-",
    },
    {
      code: "view_authorized_contact",
      title: "Contato Autorizado",
      align: "center",
      dataIndex: "authorized_contact",
      render: (_, item) => item.authorized_contact || "-",
    },
    {
      code: "view_contact_active",
      title: "Ativo",
      dataIndex: "active",
      key: "active",
      render: (field) => {
        if (field === 1) {
          return <Tag color="green">Ativo</Tag>;
        } else {
          return <Tag color="red">Inativo</Tag>;
        }
      },
    },
    {
      code: "view_contact_portal",
      title: "Área Logada",
      dataIndex: "id",
      key: "id",
      render: (a, item) => (
        <CustomerPortalButton customerPortal={customerPortal} item={item} />
      ),
    },
    {
      code: "view_contact_portal",
      title: "Área Logada",
      dataIndex: "id",
      key: "id",
      render: (a, item) => {
        return (
          <Row justify="center">
            <Tooltip title="Faça o reset de senha deste acesso no portal Área Logada">
              <Popconfirm
                title="Tem certeza que deseja resetar a senha deste acesso?"
                onConfirm={() => resetUserAccess(item.email)}
                cancelText="Cancelar"
              >
                <Button type="link" style={{ padding: "0" }}>
                  <Tag color="warning">Resetar Senha</Tag>
                </Button>
              </Popconfirm>
            </Tooltip>
          </Row>
        );
      },
    },
    {
      code: "mfa_reset",
      title: "Resetar MFA",
      dataIndex: "mfa_reset",
      key: "mfa_reset",
      align: "center",
      render: (mfa_reset, item) => {
        return (
          <ResetMFAButton
            resetMfa={resetMfa}
            item={item}
            setSelectedContact={() => setSelectedContact(item?.email)}
            setTicket={(e) => setTicket(e)}
            ticket={ticket}
            selectedContact={selectedContact}
            mfaLoading={mfaLoading}
          />
        );
      },
    },
    {
      code: "view_contact_edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      render: (field, item) => {
        return (
          <ContactEditModal
            getContacts={handleGetCurrentContacts}
            clients={clients}
            client={client}
            contact={item}
          />
        );
      },
    },

    {
      code: "view_contact_actions",
      title: "Ações",
      dataIndex: "active",
      key: "active",
      render: (active, item) => (
        <ToggleActive item={item} changeActive={changeActive} active={active} />
      ),
    },
  ];

  const pagination = {
    data: [],
  };

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyITSM,
          verifyFirstName,
          verifyLastName,
          verifyPhone,
          verifyEmail,
          verifyDSMID = false;

        if (!e.email || e.email === "") return null;

        if (e.identifications.itsm_id) {
          verifyITSM = e.identifications.itsm_id
            .toString()
            .includes(search.toLowerCase());
        }

        if (e.dsm_id) {
          verifyDSMID = e.dsm_id
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.names.first_name) {
          verifyFirstName = e.names.first_name
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.names.last_name) {
          verifyLastName = e.names.last_name
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.phone) {
          verifyPhone = e.phone.toLowerCase().includes(search.toLowerCase());
        }

        if (e.email) {
          verifyEmail = e.email.toLowerCase().includes(search.toLowerCase());
        }

        if (
          verifyITSM ||
          verifyFirstName ||
          verifyLastName ||
          verifyPhone ||
          verifyEmail ||
          verifyDSMID
        )
          return e;
      });
    }

    return filteredData;
  };

  const tableData = useMemo(() => {
    let filteredData = contacts || [];

    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    return filteredData;
  }, [search, contacts]);

  return (
    <>
      <Row justify="center">
        <Tooltip title="Visualizar contatos">
          <Button
            data-testid="open-contacts-modal"
            type="text"
            style={{
              color: client?.contacts?.length ? "#00B050" : "#333",
            }}
            onClick={handleOpenModal}
          >
            <UserOutlined />
          </Button>
        </Tooltip>
      </Row>
      <Modal
        data-testid={
          showModal !== true ? "client-contacts-modal" : "opened-contacts-modal"
        }
        width="65vw"
        onCancel={() => setShowModal(false)}
        title="Contatos"
        open={showModal}
        closable={false}
        footer={[
          <Button type="primary" onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        <Row gutter={[12, 0]} justify="center">
          <Col>
            {permissions
              ?.map((permission) => {
                return permission.code;
              })
              .includes("view_contact_add") ? (
              <ContactCreateModal
                clients={clients}
                client={client}
                getContacts={handleGetCurrentContacts}
                refreshContacts={handleRefreshContacts}
              />
            ) : (
              ""
            )}
          </Col>
          <Col>
            <Input
              onChange={(e) => setSearch(e.target.value)}
              style={{
                width: "300px",
                height: "32px",
                borderRadius: "7px",
                marginBottom: "2em",
              }}
              placeholder="Buscar contato..."
            />
          </Col>
        </Row>
        <Row justify="end">
          {tableData && tableData.length > 0 ? (
            <Text style={{ margin: "5px 10px 5px 0px" }}>
              Total: {tableData.length}
            </Text>
          ) : null}
        </Row>
        <Row
          align="center"
          style={{
            flexWrap: "nowrap",
            overflowX: "auto",
            paddingBottom: "10px",
          }}
        >
          <Table
            loading={loading}
            style={{ minWidth: "100%" }}
            dataSource={tableData}
            columns={columns.filter((e) =>
              permissions
                ?.map((permission) => {
                  return permission.code;
                })
                .includes(e.code)
            )}
            pagination={pagination}
            scroll={{ x: 2000, y: 400 }}
          />
        </Row>
      </Modal>
    </>
  );
};
