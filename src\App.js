import "./App.css";
import Router from "./Routes/Router";
import ErrorLogger from "./components/ErrorLogger";
import { ConfigProvider } from "antd";

// Configuração do tema DSM para Antd v5 - Conservadora
const dsmTheme = {
  token: {
    // Apenas cores essenciais - mantendo tamanhos e pesos padrão
    colorPrimary: '#00B050',
    colorSuccess: '#00B050',
    colorLink: '#00B050',
    borderRadius: 6,
    // Removendo configurações de tipografia que podem estar causando problemas
  },
  components: {
    // Configurações mínimas apenas para cores - sem alterar tamanhos ou pesos
    Button: {
      colorPrimary: '#00B050',
      colorPrimaryHover: 'rgba(0, 176, 80, 0.8)',
      colorPrimaryActive: '#009944',
    },
    Menu: {
      itemSelectedBg: '#00B050',
      itemSelectedColor: '#ffffff',
    },
    Pagination: {
      itemActiveBg: '#00B050',
      itemActiveColorDisabled: '#ffffff',
    },
    Switch: {
      colorPrimary: '#00B050',
    },
    Checkbox: {
      colorPrimary: '#00B050',
    },
    Radio: {
      colorPrimary: '#00B050',
    },
    Input: {
      colorPrimaryHover: '#00B050',
      colorPrimary: '#00B050',
    },
    Select: {
      colorPrimary: '#00B050',
      colorPrimaryHover: '#00B050',
    },
    DatePicker: {
      colorPrimary: '#00B050',
    },
    Tabs: {
      colorPrimary: '#00B050',
      inkBarColor: '#00B050',
    },
    Progress: {
      colorSuccess: '#00B050',
    },
    Tag: {
      colorSuccess: '#00B050',
    },
    Alert: {
      colorSuccessBg: 'rgba(0, 176, 80, 0.2)',
      colorSuccessBorder: '#00B050',
    },
    Message: {
      colorSuccess: '#00B050',
    },
    Notification: {
      colorSuccess: '#00B050',
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={dsmTheme}>
      <Router />
      {/* Only show ErrorLogger in development */}
      {process.env.NODE_ENV === 'development' && <ErrorLogger />}
    </ConfigProvider>
  );
}

export default App;
