/**
 * Hook personalizado para usar a configuração dinâmica da API
 * Facilita o uso da configuração dinâmica em componentes React
 */

import { useState, useEffect, useCallback } from 'react';
import { dynamicApiConfig, makeConfiguredRequest } from '../services/dynamicApiConfig';

export const useDynamicApi = () => {
  const [isReady, setIsReady] = useState(false);
  const [config, setConfig] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkConfig = () => {
      setIsReady(dynamicApiConfig.isReady());
      setConfig(dynamicApiConfig.getConfig());
    };

    checkConfig();
    
    const interval = setInterval(checkConfig, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const makeRequest = useCallback(async (endpoint, options = {}) => {
    try {
      setError(null);
      return await makeConfiguredRequest(endpoint, options);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  const getEndpointUrl = useCallback((endpoint) => {
    if (!isReady) {
      return null;
    }
    return dynamicApiConfig.getEndpointUrl(endpoint);
  }, [isReady]);

  const refreshConfig = useCallback(async () => {
    try {
      setError(null);
      await dynamicApiConfig.refreshConfig();
      setIsReady(dynamicApiConfig.isReady());
      setConfig(dynamicApiConfig.getConfig());
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  return {
    isReady,
    config,
    error,
    makeRequest,
    getEndpointUrl,
    refreshConfig
  };
};

export const useApiEndpoint = (endpoint) => {
  const { isReady, getEndpointUrl } = useDynamicApi();
  
  const [url, setUrl] = useState(null);

  useEffect(() => {
    if (isReady) {
      setUrl(getEndpointUrl(endpoint));
    }
  }, [isReady, endpoint, getEndpointUrl]);

  return url;
};

// ✅ REMOVIDO: useMfaRequest não é necessário
// O componente MFA processa códigos da URL, não faz requisições para /mfa endpoint
// export const useMfaRequest = () => {
//   const { makeRequest } = useDynamicApi();
//   const fetchMfaData = useCallback(async (code, idToken) => {
//     return makeRequest('/mfa', {
//       method: 'GET',
//       params: { code },
//       headers: { Authorization: `Bearer ${idToken}` }
//     });
//   }, [makeRequest]);
//   return { fetchMfaData };
// };

export const useCognitoRequest = () => {
  const { makeRequest } = useDynamicApi();

  const fetchCognitoData = useCallback(async (idToken) => {
    return makeRequest('/cognito/read', {
      method: 'GET',
      headers: { Authorization: `Bearer ${idToken}` }
    });
  }, [makeRequest]);

  return { fetchCognitoData };
};

export default useDynamicApi;
