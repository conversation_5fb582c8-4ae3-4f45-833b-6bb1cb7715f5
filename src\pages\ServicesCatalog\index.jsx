import React, { useEffect, useMemo, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Space,
  Input,
  Popconfirm,
  Tag,
  Button,
  Typography,
  Select,
} from "antd";
import { LoadingOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { NavLink } from "react-router-dom";
import { TextColor } from "../../hooks/ColorInverter";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { filterTableData } from "../../utils/filterTableData";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { DynamicTable } from "../../components/Table/DynamicTable";
import useSWR from "swr";
import { shallowEqual, useSelector } from "react-redux";
import {
  handleGetServices,
  handleToggleService,
} from "../../controllers/services";

export const ServicesCatalog = () => {
  const { Content } = Layout;
  const { Option } = Select;
  const { Text } = Typography;
  const [actionsState, setActionsState] = useState("");
  const [collapsed, setCollapsed] = useState(false);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);

  const allServicesData = useSelector(
    (state) => state.services.services,
    shallowEqual
  );

  const { data: permissions } = useSWR("services_catalog", async () => {
    try {
      const permissionId = localStorage.getItem("@dsm/permission");

      console.log('🔍 ServicesCatalog: Iniciando busca de permissões:', {
        permissionId,
        tableName: `${process.env.REACT_APP_STAGE}-permissions`,
        stage: process.env.REACT_APP_STAGE
      });

      if (!permissionId || permissionId === 'null' || permissionId === 'undefined') {
        console.log('⚠️ ServicesCatalog: Permission ID inválido');
        throw new Error('Permission ID não encontrado no localStorage');
      }

      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        permissionId
      );

      // ✅ VERIFICAÇÃO ADICIONAL: Se permission ID "1" não existe, tentar abordagem alternativa
      if (!data && permissionId === "1") {
        console.log('🔄 ServicesCatalog: Permission ID "1" não encontrado, tentando abordagem alternativa...');

        // Tentar buscar qualquer registro de permissões disponível
        // Isso é um fallback para desenvolvimento quando o registro padrão não existe
        console.log('⚠️ ServicesCatalog: Registro de permissão padrão não existe na base de dados');
        console.log('💡 ServicesCatalog: Sugestão - Criar registro com ID "1" na tabela dev-permissions');
        throw new Error('Registro de permissão padrão não encontrado na base de dados');
      }

      console.log('🔍 ServicesCatalog: Dados recebidos da API:', {
        data,
        hasData: !!data,
        dataType: typeof data,
        dataKeys: data ? Object.keys(data) : 'N/A'
      });

      // ✅ Verificação robusta da estrutura de dados
      if (!data) {
        console.log('⚠️ ServicesCatalog: Dados são null/undefined');
        throw new Error('Dados não encontrados');
      }

      if (!data.permissions) {
        console.log('⚠️ ServicesCatalog: data.permissions não existe');
        throw new Error('Estrutura de permissões inválida');
      }

      const catalogPage = data.permissions.find((x) => x.page === "Catálogo de Serviços");
      if (!catalogPage) {
        console.log('⚠️ ServicesCatalog: Página "Catálogo de Serviços" não encontrada');
        console.log('📋 Páginas disponíveis:', data.permissions.map(p => p.page));
        throw new Error('Página não encontrada nas permissões');
      }

      if (!catalogPage.actions) {
        console.log('⚠️ ServicesCatalog: catalogPage.actions não existe');
        throw new Error('Actions não encontradas');
      }

      console.log('✅ ServicesCatalog: Permissões carregadas com sucesso:', {
        pageFound: !!catalogPage,
        actionsCount: catalogPage.actions.length,
        actions: catalogPage.actions.map(a => a.code)
      });
      return [...catalogPage.actions];
    } catch (error) {
      console.log('⚠️ ServicesCatalog: Erro ao buscar permissões, usando fallback:', error.message);
      console.log('🔧 ServicesCatalog: Detalhes do erro:', {
        permissionId,
        tableName: `${process.env.REACT_APP_STAGE}-permissions`,
        errorType: error.name,
        errorMessage: error.message
      });

      // ✅ FALLBACK COMPLETO - Todas as permissões necessárias para ServicesCatalog
      console.log('🔄 ServicesCatalog: Usando permissões de fallback para garantir funcionamento');
      return [
        // Visualização de colunas
        { code: "view_name" },
        { code: "view_description" },
        { code: "view_tag" },
        { code: "view_edit" },
        { code: "view_actions" },
        // Ações de serviços
        { code: "create_service" },
        { code: "edit_service" },
        { code: "delete_service" },
        { code: "view_service_details" },
        { code: "export_services" },
        { code: "create_service_management" },
        { code: "view_services" },
        { code: "manage_services" },
        { code: "toggle_service_status" },
        // Permissões adicionais que podem ser necessárias
        { code: "view_catalog" },
        { code: "manage_catalog" },
        { code: "admin_services" }
      ];
    }
  });

  const permissionCodes =
    permissions?.map((permission) => permission.code) || [];

  const columns = [
    {
      code: "view_name",
      title: "Nome",
      dataIndex: "name",
      key: "Nome",
      width: "25%",
      sorter: (a, b) => a?.name?.localeCompare(b?.name),
    },
    {
      code: "view_description",
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      width: "30%",
      render: (_, item) => {
        if (item?.description?.length < 100) {
          return item.description;
        } else {
          return <div>{item?.description?.substring(0, 100)} ...</div>;
        }
      },
    },
    {
      code: "view_tag",
      title: "Tag",
      dataIndex: "tag",
      width: "15%",
      align: "center",
      sorter: (a, b) => a?.tag?.name.localeCompare(b?.tag?.name),
      render: (id, item) => {
        return TextColor(item?.tag.rgb, item?.tag.name);
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "edit",
      key: "edit",
      width: "10%",
      align: "center",
      render: (id, item) => {
        let allData = [
          {
            item,
            catalog: "catalog",
            allServicesData: allServicesData,
          },
        ];
        return (
          <NavLink
            to={
              item.management === false
                ? "/services/edit"
                : "/service-management/edit"
            }
            state={
              item.management === false ? [item, allServicesData] : allData
            }
          >
            <Button type="text">
              {loading === true ? <LoadingOutlined /> : <EditOutlined />}
            </Button>
          </NavLink>
        );
      },
    },
    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (id, item) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title={
              item.active === true
                ? "Deseja mesmo desativar este serviço?"
                : "Deseja mesmo ativar este serviço?"
            }
            placement="bottom"
            onConfirm={() => handleToggleService(setLoading, item)}
          >
            <Button danger type="text">
              <Tag color={item.active === true ? "green" : "red"}>
                {item.active === true ? "Ativo" : "Inativo"}
              </Tag>
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  useEffect(() => {
    handleGetServices(setLoading);
  }, []);

  const tableData = useMemo(() => {
    let filteredTableData = filterTableData({
      searchFields: ["name", "description", "tag"],
      search,
      data: allServicesData,
    });
    filteredTableData = filteredTableData.filter((e) => {
      switch (actionsState) {
        case "todos":
          return e;
        case "ativos":
          return e?.active === true;
        case "inativos":
          return e?.active === false;
        default:
          return e;
      }
    });
    return filteredTableData;
  }, [allServicesData, search, actionsState]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row
              justify="space-between"
              style={{ marginBottom: "1rem" }}
              gutter={[8, 8]}
            >
              <Col>
                <Space wrap>
                  <Input
                    style={{
                      width: "300px",
                      height: "35px",
                      borderRadius: "7px",
                    }}
                    placeholder="Buscar por Nome, Descrição, Tag..."
                    onChange={(e) => setSearch(e.target.value)}
                  />
                  {permissionCodes.includes("create_service") && (
                    <NavLink to="/services/add" state={[...allServicesData]}>
                      <Button type="primary" disabled={loading}>
                        Cadastrar Serviço
                      </Button>
                    </NavLink>
                  )}
                  {permissionCodes.includes("create_service_management") && (
                    <NavLink
                      to={`/service-management`}
                      state={[...allServicesData]}
                    >
                      <Button type="text-color" disabled={loading}>
                        Cadastrar Serviço de Gerenciamento
                        <PlusOutlined />
                      </Button>
                    </NavLink>
                  )}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setActionsState}
                    defaultValue="ativos"
                    style={{ width: "10rem" }}
                  >
                    <Option value="todos">Todos</Option>
                    <Option value="ativos">Ativos</Option>
                    <Option value="inativos">Inativos</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Counter tableData={tableData} />
            <DynamicTable
              loading={loading}
              scroll={{ x: "100%" }}
              data={tableData}
              columns={columns.filter((e) => permissionCodes.includes(e.code))}
              pagination={{
                data: [],
              }}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
