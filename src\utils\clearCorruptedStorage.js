/**
 * Utility to clear corrupted localStorage data that might cause Redux serialization issues
 */

export const clearCorruptedStorage = () => {
  try {
    const keysToCheck = [
      'persist:root',
      'persist:consumption',
      'persist:auth',
      'persist:invoices'
    ];

    keysToCheck.forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        try {
          const parsed = JSON.parse(item);
          
          if (key === 'persist:root' && parsed.consumption) {
            const consumption = JSON.parse(parsed.consumption);
            
            if (consumption.dtEnd && typeof consumption.dtEnd === 'object' && consumption.dtEnd._isAMomentObject) {
              console.warn('Found corrupted moment object in consumption state, clearing...');
              localStorage.removeItem(key);
              return;
            }
            
            if (consumption.dtStart && typeof consumption.dtStart === 'object' && consumption.dtStart._isAMomentObject) {
              console.warn('Found corrupted moment object in consumption state, clearing...');
              localStorage.removeItem(key);
              return;
            }
          }
          
        } catch (parseError) {
          console.warn(`Corrupted localStorage item found: ${key}, clearing...`);
          localStorage.removeItem(key);
        }
      }
    });

  } catch (error) {
    console.error('Error during storage cleanup:', error);
  }
};

/**
 * Clear all Redux persist data
 */
export const clearAllPersistData = () => {
  try {
    const keys = Object.keys(localStorage);
    const persistKeys = keys.filter(key => key.startsWith('persist:'));
    
    persistKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Cleared: ${key}`);
    });
    
    console.log('All persist data cleared');
  } catch (error) {
    console.error('Error clearing persist data:', error);
  }
};

/**
 * Initialize storage cleanup on app start
 */
export const initializeStorageCleanup = () => {
  // Clear corrupted data on app initialization
  clearCorruptedStorage();
  
  // Set up periodic cleanup (every 5 minutes)
  setInterval(() => {
    clearCorruptedStorage();
  }, 5 * 60 * 1000);
};

export default {
  clearCorruptedStorage,
  clearAllPersistData,
  initializeStorageCleanup
};
