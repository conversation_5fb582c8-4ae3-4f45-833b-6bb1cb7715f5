import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./styles/theme.css";
import "./styles/antd-custom.css";
import "./styles/layout-fixes.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { store, persistor } from "./store/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { initializeStorageCleanup } from "./utils/clearCorruptedStorage";
import "./utils/diagnostics"; // Importar funções de diagnóstico
import { initializePerformanceOptimizations } from "./utils/performanceOptimization";

// Global error handler for debugging
window.addEventListener('error', (event) => {
  console.group('🚨 GLOBAL ERROR CAPTURED');
  console.error('Error:', event.error);
  console.error('Message:', event.message);
  console.error('Filename:', event.filename);
  console.error('Line:', event.lineno);
  console.error('Column:', event.colno);
  console.error('Stack:', event.error?.stack);
  console.groupEnd();

  // Store error in sessionStorage to persist across reloads
  const errorLog = {
    timestamp: new Date().toISOString(),
    message: event.message,
    filename: event.filename,
    line: event.lineno,
    column: event.colno,
    stack: event.error?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10))); // Keep last 10 errors
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.group('🚨 UNHANDLED PROMISE REJECTION CAPTURED');
  console.error('Reason:', event.reason);
  console.error('Promise:', event.promise);
  if (event.reason?.stack) {
    console.error('Stack:', event.reason.stack);
  }
  console.groupEnd();

  // Store promise rejection in sessionStorage
  const errorLog = {
    timestamp: new Date().toISOString(),
    type: 'unhandledrejection',
    reason: event.reason?.toString(),
    stack: event.reason?.stack
  };

  const existingErrors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  existingErrors.push(errorLog);
  sessionStorage.setItem('errorLog', JSON.stringify(existingErrors.slice(-10)));
});

// Initialize storage cleanup before app starts
initializeStorageCleanup();

// Initialize performance optimizations
initializePerformanceOptimizations();

// Add global debug functions
window.showErrors = () => {
  const errors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
  console.group('📋 ALL CAPTURED ERRORS');
  errors.forEach((error, index) => {
    console.group(`Error ${index + 1} - ${error.timestamp}`);
    console.error('Message:', error.message || error.reason);
    if (error.filename) {
      console.error('File:', `${error.filename}:${error.line}:${error.column}`);
    }
    if (error.stack) {
      console.error('Stack:', error.stack);
    }
    console.groupEnd();
  });
  console.groupEnd();
  return errors;
};

window.clearErrors = () => {
  sessionStorage.removeItem('errorLog');
  console.log('✅ All errors cleared');
};

console.log('🐛 Debug commands available:');
console.log('  - showErrors() - Display all captured errors');
console.log('  - clearErrors() - Clear all captured errors');
console.log('  - runDiagnostics() - Complete system diagnostics');
console.log('  - testAuthentication() - Test JWT authentication');
console.log('  - cleanupCorruptedData() - Clean corrupted localStorage');
console.log('  - testBackendHealth() - Test backend API health');
console.log('  - diagnoseInvalidGrant() - Analyze invalid_grant error');
console.log('  - diagnoseCorsHttpOnly() - Diagnose CORS/HttpOnly backend issues');

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
