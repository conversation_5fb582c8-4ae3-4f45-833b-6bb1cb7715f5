import { Row, Col, Image, Space, Divider } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import React, { useEffect, useRef } from "react";
import { jwtDecode } from "jwt-decode";
import axios from "axios";
// Importações removidas para evitar CORS
// import { dynamoGet } from "../../service/apiDsmDynamo";
// import { cognitoPutRole } from "../../service/apiCognito";

export default function MFA() {
  const navigate = useNavigate();

  const code = window.location.href.split("code=")[1];

  // Prevenir processamento duplo (React StrictMode executa useEffect 2x)
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [hasProcessed, setHasProcessed] = React.useState(false);
  const processedRef = useRef(false); // Persiste entre re-renders

  const authenticate = async () => {
    // Chave única para este código específico
    const processingKey = `mfa_processing_${code}`;

    // Verificar se já está sendo processado (persiste entre instâncias)
    if (sessionStorage.getItem(processingKey)) {
      console.log("⏸️ Código já está sendo processado em outra instância, ignorando...");
      return;
    }

    // Prevenir processamento duplo local
    if (isProcessing || hasProcessed || processedRef.current) {
      console.log("⏸️ Processamento já realizado localmente, ignorando...");
      return;
    }

    try {
      // Marcar como processando globalmente
      sessionStorage.setItem(processingKey, 'true');
      setIsProcessing(true);
      console.log("🚀 Iniciando autenticação MFA...");
      console.log("Código recebido:", code);

      if (!code) {
        console.error("❌ Código não encontrado na URL");
        return navigate("/login");
      }

      // Para desenvolvimento/teste, verificar se é um código de teste
      if (code === 'test123' || code.startsWith('test')) {
        console.log("Modo de teste detectado - simulando autenticação");

        // Simular dados de usuário para teste
        localStorage.setItem("@dsm/mail", "<EMAIL>");
        localStorage.setItem("@dsm/name", "test");
        localStorage.setItem("@dsm/username", "test");
        localStorage.setItem("@dsm/permission", "1"); // ID de permissão básica
        localStorage.setItem("@dsm/time", new Date());
        localStorage.setItem("jwt", "test-jwt-token");

        console.log("Autenticação de teste concluída");
        return navigate("/");
      }

      const response = await axios.post(process.env.REACT_APP_COGNITO_PARSE, {
        code: code,
      });

      console.log("Resposta da API:", response.data);

      // Verificar se houve erro na resposta
      if (response.data?.data?.error) {
        console.error("Erro na API:", response.data.data.error);

        // Se o erro for invalid_grant mas ainda temos um token, continuar
        if (response.data.data.error === 'invalid_grant' && response.data?.data?.id_token) {
          console.warn("⚠️ invalid_grant detectado, mas token presente. Continuando...");
        } else {
          console.error("❌ Erro crítico na API, redirecionando para login");
          console.error("Estrutura completa:", JSON.stringify(response.data, null, 2));
          return navigate("/login");
        }
      }

      const id_token = response.data?.data?.id_token;



      if (!id_token || typeof id_token !== 'string') {
        console.error("❌ Token inválido recebido:", id_token);
        console.error("Estrutura completa da resposta:", response.data);
        return navigate("/login");
      }

      console.log("✅ Token JWT válido recebido");

      const { email } = jwtDecode(id_token);

    localStorage.setItem("@dsm/mail", email);
    localStorage.setItem("@dsm/name", email.split("@")[0]);
    localStorage.setItem("@dsm/username", email.split("@")[0]);
    localStorage.setItem("jwt", id_token);

    // Sistema simplificado sem CORS - usar informações do token JWT
    console.log("Email extraído do token:", email);

    // Definir permissão básica baseada no email (sem chamadas externas)
    let permission = "1"; // Permissão básica padrão

    // Mapear permissões baseadas no domínio do email (sem APIs externas)
    if (email.includes("@darede.com.br")) {
      // Usuários da Darede têm permissão básica
      permission = "1";
    }

    // Salvar dados no localStorage
    localStorage.setItem("@dsm/permission", permission);
    localStorage.setItem("@dsm/time", new Date());

    console.log("Autenticação concluída com sucesso");
    console.log("Email:", email);
    console.log("Permissão:", permission);

    console.log("🚀 Redirecionando para home (/)...");
    setHasProcessed(true); // Marcar como processado com sucesso
    processedRef.current = true; // Persiste entre re-renders

    // Limpar marcação de processamento
    sessionStorage.removeItem(processingKey);

    navigate("/");
    } catch (error) {
      console.error("Erro na autenticação:", error);
      console.error("Detalhes do erro:", error.response?.data || error.message);

      // Limpar marcação de processamento em caso de erro
      sessionStorage.removeItem(processingKey);

      navigate("/login");
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    async function getData() {
      await authenticate();
    }

    getData();
  }, []);

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider>
              Aguarde um momento...
              <br />
              Estamos te autenticando!
            </Divider>
          </Space>

          <LoadingOutlined style={{ fontSize: "48px" }} />
        </Col>
      </Row>
    </>
  );
}
