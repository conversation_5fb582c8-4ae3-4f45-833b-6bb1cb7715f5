import React, { createContext, useContext, useEffect, useState } from 'react';
import { message } from 'antd';

// Security Context
const SecurityContext = createContext(null);

/**
 * Security Provider Component
 * Implements client-side security measures
 */
export const SecurityProvider = ({ children }) => {
  const [csrfToken, setCsrfToken] = useState(null);
  const [securityConfig, setSecurityConfig] = useState({
    enableXSSProtection: true,
    enableCSRFProtection: true,
    enableContentSecurityPolicy: true,
    enableInputSanitization: true,
  });

  // Initialize security measures
  useEffect(() => {
    initializeSecurity();
  }, []);

  const initializeSecurity = async () => {
    try {
      // Set up Content Security Policy
      if (securityConfig.enableContentSecurityPolicy) {
        setupCSP();
      }

      // Get CSRF token
      if (securityConfig.enableCSRFProtection) {
        await fetchCSRFToken();
      }

      // Set up XSS protection
      if (securityConfig.enableXSSProtection) {
        setupXSSProtection();
      }

      // Set up input sanitization
      if (securityConfig.enableInputSanitization) {
        setupInputSanitization();
      }

      // Monitor for security violations
      setupSecurityMonitoring();

    } catch (error) {
      console.error('Security initialization failed:', error);
    }
  };

  /**
   * Set up Content Security Policy
   */
  const setupCSP = () => {
    // Add CSP meta tag if not already present
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https: blob:",
        "connect-src 'self' https://api.dsm.darede.com.br https://hml.dsm.darede.com.br wss: ws:",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "object-src 'none'"
      ].join('; ');
      
      document.head.appendChild(meta);
    }

    // Listen for CSP violations
    document.addEventListener('securitypolicyviolation', (event) => {
      console.warn('CSP Violation:', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
      });

      // Report to monitoring service
      reportSecurityViolation('csp', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
      });
    });
  };

  /**
   * Fetch CSRF token from server
   */
  const fetchCSRFToken = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_PERMISSION}auth/csrf-token`, {
        credentials: 'omit', // TEMPORÁRIO: Removido para testar com CORS *
      });
      
      if (response.ok) {
        const data = await response.json();
        setCsrfToken(data.csrfToken);
      }
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
    }
  };

  /**
   * Set up XSS protection
   */
  const setupXSSProtection = () => {
    // Override dangerous DOM methods
    const originalInnerHTML = Element.prototype.__lookupSetter__('innerHTML');
    if (originalInnerHTML) {
      Object.defineProperty(Element.prototype, 'innerHTML', {
        set: function(value) {
          const sanitizedValue = sanitizeHTML(value);
          originalInnerHTML.call(this, sanitizedValue);
        },
        get: Element.prototype.__lookupGetter__('innerHTML'),
      });
    }

    // Monitor for suspicious script injections
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check for suspicious script tags
            if (node.tagName === 'SCRIPT' && !node.hasAttribute('data-safe')) {
              console.warn('Suspicious script injection detected:', node);
              reportSecurityViolation('xss', {
                element: node.outerHTML,
                location: window.location.href,
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  };

  /**
   * Set up input sanitization
   */
  const setupInputSanitization = () => {
    // Add event listeners to all forms
    document.addEventListener('submit', (event) => {
      const form = event.target;
      if (form.tagName === 'FORM') {
        const formData = new FormData(form);
        let hasSuspiciousContent = false;

        for (const [key, value] of formData.entries()) {
          if (typeof value === 'string' && containsSuspiciousContent(value)) {
            hasSuspiciousContent = true;
            console.warn('Suspicious form input detected:', { field: key, value });
          }
        }

        if (hasSuspiciousContent) {
          event.preventDefault();
          message.error('Entrada suspeita detectada. Por favor, revise os dados.');
          reportSecurityViolation('suspicious_input', {
            form: form.action,
            location: window.location.href,
          });
        }
      }
    });
  };

  /**
   * Set up security monitoring
   */
  const setupSecurityMonitoring = () => {
    // Monitor for console access attempts
    let devtools = false;
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools) {
          devtools = true;
          console.warn('Developer tools detected');
          reportSecurityViolation('devtools', {
            location: window.location.href,
            userAgent: navigator.userAgent,
          });
        }
      } else {
        devtools = false;
      }
    }, 1000);

    // Monitor for suspicious URL changes
    let lastURL = window.location.href;
    new MutationObserver(() => {
      const currentURL = window.location.href;
      if (currentURL !== lastURL) {
        if (containsSuspiciousContent(currentURL)) {
          console.warn('Suspicious URL detected:', currentURL);
          reportSecurityViolation('suspicious_url', {
            url: currentURL,
            previousUrl: lastURL,
          });
        }
        lastURL = currentURL;
      }
    }).observe(document, { subtree: true, childList: true });
  };

  /**
   * Sanitize HTML content
   */
  const sanitizeHTML = (html) => {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  };

  /**
   * Check for suspicious content
   */
  const containsSuspiciousContent = (content) => {
    const suspiciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /document\.write/gi,
      /innerHTML/gi,
      /outerHTML/gi,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(content));
  };

  /**
   * Report security violations
   */
  const reportSecurityViolation = (type, details) => {
    // Send to monitoring service
    if (process.env.REACT_APP_SECURITY_ENDPOINT) {
      fetch(`${process.env.REACT_APP_SECURITY_ENDPOINT}/violations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          details,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      }).catch(error => {
        console.error('Failed to report security violation:', error);
      });
    }
  };

  /**
   * Sanitize input value
   */
  const sanitizeInput = (value) => {
    if (typeof value !== 'string') return value;
    
    return value
      .replace(/[<>]/g, '') // Remove < and >
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .replace(/script/gi, 'scr1pt') // Neutralize script tags
      .trim();
  };

  /**
   * Create secure fetch wrapper
   */
  const secureFetch = async (url, options = {}) => {
    const secureOptions = {
      ...options,
      headers: {
        ...options.headers,
        ...(csrfToken && { 'X-CSRF-Token': csrfToken }),
        'X-Requested-With': 'XMLHttpRequest',
      },
      credentials: 'omit', // TEMPORÁRIO: Removido para testar com CORS *
    };

    return fetch(url, secureOptions);
  };

  const contextValue = {
    csrfToken,
    securityConfig,
    setSecurityConfig,
    sanitizeInput,
    secureFetch,
    reportSecurityViolation,
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

/**
 * Hook to use security context
 */
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  
  return context;
};

/**
 * HOC for secure components
 */
export const withSecurity = (WrappedComponent) => {
  return function SecureComponent(props) {
    return (
      <SecurityProvider>
        <WrappedComponent {...props} />
      </SecurityProvider>
    );
  };
};

export default SecurityProvider;
