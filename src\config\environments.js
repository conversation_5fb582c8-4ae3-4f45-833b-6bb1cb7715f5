/**
 * Environment Configuration
 * Centralized configuration for different environments
 */

export const ENVIRONMENTS = {
  LOCAL: 'local',
  DEV: 'dev', 
  HML: 'hml',
  PROD: 'prod'
};

/**
 * Get current environment based on hostname or REACT_APP_STAGE
 */
export const getCurrentEnvironment = () => {
  // First check environment variable
  const envStage = process.env.REACT_APP_STAGE;
  if (envStage) {
    return envStage.toLowerCase();
  }

  // Fallback to hostname detection
  const hostname = window.location.hostname;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return ENVIRONMENTS.LOCAL;
  }
  
  if (hostname.includes('dev.dsm.com.br')) {
    return ENVIRONMENTS.DEV;
  }
  
  if (hostname.includes('hml.dsm.com.br')) {
    return ENVIRONMENTS.HML;
  }
  
  if (hostname.includes('dsm.com.br')) {
    return ENVIRONMENTS.PROD;
  }
  
  // Default to local for unknown hostnames
  return ENVIRONMENTS.LOCAL;
};

/**
 * Environment-specific configurations
 */
export const getEnvironmentConfig = (environment = getCurrentEnvironment()) => {
  const configs = {
    [ENVIRONMENTS.LOCAL]: {
      name: 'Local Development',
      apiBaseUrl: process.env.REACT_APP_API_PERMISSION || 'http://localhost:8000/',
      // Serverless Offline adds /local prefix automatically
      apiPrefix: 'local/',
      frontendUrl: 'http://localhost:3000',
      cognitoParseUrl: 'http://localhost:8000/local/cognito/parse',
      isProduction: false,
      enableDebug: true,
      enablePerformanceMonitoring: true,
      enableSecurityLogging: true,
      cookieDomain: 'localhost',
      // AWS Cognito Configuration for Local
      cognito: {
        region: 'us-east-1',
        userPoolId: process.env.REACT_APP_COGNITO_USER_POOL_ID || 'us-east-1_XXXXXXXXX',
        clientId: process.env.REACT_APP_COGNITO_CLIENT_ID || 'xxxxxxxxxxxxxxxxxxxxxxxxxx',
        domain: process.env.REACT_APP_COGNITO_DOMAIN || 'dsm-auth-local',
        redirectSignIn: 'http://localhost:3000/mfa',
        redirectSignOut: 'http://localhost:3000/login',
        responseType: 'code',
        scope: ['email', 'openid', 'profile']
      },
      corsOrigins: [
        'http://localhost:3000',
        'http://localhost:8000',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8000'
      ]
    },
    
    [ENVIRONMENTS.DEV]: {
      name: 'Development',
      apiBaseUrl: process.env.REACT_APP_API_PERMISSION || 'https://api.dev.dsm.com.br/',
      frontendUrl: 'https://dev.dsm.com.br',
      cognitoParseUrl: 'https://api.dev.dsm.com.br/cognito/parse',
      isProduction: false,
      enableDebug: true,
      enablePerformanceMonitoring: true,
      enableSecurityLogging: true,
      cookieDomain: '.dsm.com.br',
      // AWS Cognito Configuration for Development
      cognito: {
        region: 'us-east-1',
        userPoolId: process.env.REACT_APP_COGNITO_USER_POOL_ID || 'us-east-1_XXXXXXXXX',
        clientId: process.env.REACT_APP_COGNITO_CLIENT_ID || 'xxxxxxxxxxxxxxxxxxxxxxxxxx',
        domain: process.env.REACT_APP_COGNITO_DOMAIN || 'dsm-auth-dev',
        redirectSignIn: 'https://dev.dsm.com.br/mfa',
        redirectSignOut: 'https://dev.dsm.com.br/login',
        responseType: 'code',
        scope: ['email', 'openid', 'profile']
      },
      corsOrigins: [
        'https://dev.dsm.com.br',
        'https://www.dev.dsm.com.br'
      ]
    },
    
    [ENVIRONMENTS.HML]: {
      name: 'Homologation',
      apiBaseUrl: process.env.REACT_APP_API_PERMISSION || 'https://api.hml.dsm.com.br/',
      frontendUrl: 'https://hml.dsm.com.br',
      cognitoParseUrl: 'https://api.hml.dsm.com.br/cognito/parse',
      isProduction: false,
      enableDebug: false,
      enablePerformanceMonitoring: true,
      enableSecurityLogging: true,
      cookieDomain: '.dsm.com.br',
      // AWS Cognito Configuration for Homologation
      cognito: {
        region: 'us-east-1',
        userPoolId: process.env.REACT_APP_COGNITO_USER_POOL_ID || 'us-east-1_XXXXXXXXX',
        clientId: process.env.REACT_APP_COGNITO_CLIENT_ID || 'xxxxxxxxxxxxxxxxxxxxxxxxxx',
        domain: process.env.REACT_APP_COGNITO_DOMAIN || 'dsm-auth-hml',
        redirectSignIn: 'https://hml.dsm.com.br/mfa',
        redirectSignOut: 'https://hml.dsm.com.br/login',
        responseType: 'code',
        scope: ['email', 'openid', 'profile']
      },
      corsOrigins: [
        'https://hml.dsm.com.br',
        'https://www.hml.dsm.com.br'
      ]
    },
    
    [ENVIRONMENTS.PROD]: {
      name: 'Production',
      apiBaseUrl: process.env.REACT_APP_API_PERMISSION || 'https://api.dsm.com.br/',
      frontendUrl: 'https://dsm.com.br',
      cognitoParseUrl: 'https://api.dsm.com.br/cognito/parse',
      isProduction: true,
      enableDebug: false,
      enablePerformanceMonitoring: false,
      enableSecurityLogging: true,
      cookieDomain: '.dsm.com.br',
      // AWS Cognito Configuration for Production
      cognito: {
        region: 'us-east-1',
        userPoolId: process.env.REACT_APP_COGNITO_USER_POOL_ID || 'us-east-1_XXXXXXXXX',
        clientId: process.env.REACT_APP_COGNITO_CLIENT_ID || 'xxxxxxxxxxxxxxxxxxxxxxxxxx',
        domain: process.env.REACT_APP_COGNITO_DOMAIN || 'dsm-auth',
        redirectSignIn: 'https://dsm.com.br/mfa',
        redirectSignOut: 'https://dsm.com.br/login',
        responseType: 'code',
        scope: ['email', 'openid', 'profile']
      },
      corsOrigins: [
        'https://dsm.com.br',
        'https://www.dsm.com.br'
      ]
    }
  };
  
  return configs[environment] || configs[ENVIRONMENTS.LOCAL];
};

/**
 * Get API URLs for current environment
 */
export const getApiUrls = (environment = getCurrentEnvironment()) => {
  const baseConfig = getEnvironmentConfig(environment);
  
  const subdomain = environment === ENVIRONMENTS.PROD ? '' : `${environment}.`;
  const domain = 'dsm.com.br';
  
  return {
    api: baseConfig.apiBaseUrl,
    proposals: `https://proposals-api.${subdomain}${domain}/`,
    reports: `https://reports-api.${subdomain}${domain}/`,
    otrs: `https://otrs-api.${subdomain}${domain}/`,
    crm: `https://crm-api.${subdomain}${domain}/`,
    billing: `https://billing.${subdomain}${domain}/`,
    customerPortal: `https://customer-portal.${subdomain}${domain}/`,
    tickets: `https://ticket-api.${subdomain}${domain}/`,
    pipedrive: `https://pipedrive-api.${subdomain}${domain}/`,
    policyAttach: `https://policy-api.${subdomain}${domain}/`,
  };
};

/**
 * Environment detection utilities
 */
export const isLocal = () => getCurrentEnvironment() === ENVIRONMENTS.LOCAL;
export const isDev = () => getCurrentEnvironment() === ENVIRONMENTS.DEV;
export const isHml = () => getCurrentEnvironment() === ENVIRONMENTS.HML;
export const isProd = () => getCurrentEnvironment() === ENVIRONMENTS.PROD;
export const isProduction = () => getEnvironmentConfig().isProduction;

/**
 * Debug utilities
 */
export const shouldEnableDebug = () => getEnvironmentConfig().enableDebug;
export const shouldEnablePerformanceMonitoring = () => getEnvironmentConfig().enablePerformanceMonitoring;
export const shouldEnableSecurityLogging = () => getEnvironmentConfig().enableSecurityLogging;

/**
 * Get environment display info
 */
export const getEnvironmentInfo = () => {
  const env = getCurrentEnvironment();
  const config = getEnvironmentConfig(env);
  
  return {
    environment: env,
    name: config.name,
    url: config.frontendUrl,
    isProduction: config.isProduction,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Environment banner for non-production environments
 */
export const getEnvironmentBanner = () => {
  const env = getCurrentEnvironment();
  
  if (env === ENVIRONMENTS.PROD) {
    return null;
  }
  
  const banners = {
    [ENVIRONMENTS.LOCAL]: {
      text: 'AMBIENTE LOCAL',
      color: '#52c41a',
      backgroundColor: '#f6ffed'
    },
    [ENVIRONMENTS.DEV]: {
      text: 'AMBIENTE DE DESENVOLVIMENTO',
      color: '#00B050',
      backgroundColor: '#f6ffed'
    },
    [ENVIRONMENTS.HML]: {
      text: 'AMBIENTE DE HOMOLOGAÇÃO',
      color: '#fa8c16',
      backgroundColor: '#fff7e6'
    }
  };
  
  return banners[env];
};

// Export current environment config as default
export default getEnvironmentConfig();
