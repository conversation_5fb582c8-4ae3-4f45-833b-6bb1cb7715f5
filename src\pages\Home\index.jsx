import { useEffect, useMemo, useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { useNavigate } from "react-router-dom";
import {
  Col,
  Row,
  Card,
  Input,
  Layout,
  Space,
  Select,
  Typography,
  Button,
  notification,
  Table,
} from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";
import useSWR from "swr";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { ViewAccessesModal } from "../../components/Modals/SwitchRoles/ViewAccessesModal";
import Cookies from "js-cookie";
import "./iframeStyle.css";
import axios from "axios";
import { capitalizeFirstLetterFromEachWord } from "../../utils/capitalizeFirstLetter";
import { authService } from "../../services/authService";

export const Home = () => {
  const navigate = useNavigate();
  const [api, contextHolder] = notification.useNotification();
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const [data, setData] = useState([]);
  const { Text } = Typography;
  const { Content } = Layout;
  const { Option } = Select;

  function uniqByKeepLast(data, key) {
    return [...new Map(data.map((x) => [key(x), x]).values())];
  }

  const permissions = useSWR("home", async () => {
    try {
      console.group("🏠 CARREGANDO PERMISSÕES DA HOME");
      console.log("Iniciando carregamento de permissões...");

      const userData = authService.getUserData();
      console.log("Dados do usuário:", userData);

      if (!userData.permission) {
        console.error("❌ Permissão não encontrada nos dados do usuário");
        throw new Error("Permissão não encontrada");
      }

      const tableName = `${process.env.REACT_APP_STAGE}-permissions`;
      const permissionId = userData.permission;

      console.log("Fazendo requisição para DynamoDB:");
      console.log("- Tabela:", tableName);
      console.log("- ID da permissão:", permissionId);
      console.log("- URL completa:", `${process.env.REACT_APP_API_DYNAMO}${tableName}/${permissionId}`);

      let data;

      try {
        data = await dynamoGetById(tableName, permissionId);
        console.log("✅ Resposta do DynamoDB:", data);
      } catch (error) {
        console.log("⚠️ Erro ao buscar permissões do backend:", error.message);
        data = null;
      }

      // FALLBACK PARA DESENVOLVIMENTO LOCAL (igual à versão original)
      if (!data || !data.permissions) {
        console.log("🔧 DESENVOLVIMENTO: Usando permissões padrão (fallback)");
        data = {
          permissions: [
            {
              page: "Home",
              actions: [
                { code: "view_all_switch_roles" },
                { code: "create_switch_role" },
                { code: "edit_switch_role" },
                { code: "delete_switch_role" },
                // ✅ Permissões específicas para colunas da tabela Home
                { code: "view_ticket" },
                { code: "view_client" },
                { code: "view_user" },
                { code: "view_actions" }
              ]
            }
          ]
        };
      }

      const homePermissions = data.permissions.find((x) => x.page === "Home");
      console.log("Permissões da Home encontradas:", homePermissions);

      if (!homePermissions) {
        console.warn("⚠️ Permissões da Home não encontradas, usando permissões padrão");
        console.groupEnd();
        return [{ code: "view_basic", name: "Visualização Básica" }];
      }

      const actions = [...homePermissions.actions];
      console.log("✅ Ações disponíveis:", actions);
      console.groupEnd();

      return actions;
    } catch (error) {
      console.group("❌ ERRO AO CARREGAR PERMISSÕES");
      console.error("Tipo do erro:", error.name);
      console.error("Mensagem:", error.message);
      console.error("Stack:", error.stack);

      if (error.message?.includes('CORS')) {
        console.error("🌐 PROBLEMA DE CORS - Backend não configurado para localhost:3000");
      } else if (error.message?.includes('Failed to fetch')) {
        console.error("📡 FALHA DE CONEXÃO - Backend offline ou bloqueado");
      } else if (error.message?.includes('401')) {
        console.error("🔐 NÃO AUTORIZADO - Token inválido ou HttpOnly ativo");
      } else if (error.message?.includes('403')) {
        console.error("🚫 ACESSO NEGADO - Sem permissão");
      }

      console.groupEnd();

      // Retornar permissões básicas em caso de erro
      return [{ code: "view_basic", name: "Visualização Básica" }];
    }
  });

  const getData = async () => {
    try {
      console.group("📊 CARREGANDO DADOS DA HOME");
      console.log("Iniciando carregamento de dados...");
      setLoading(true);
    let res = await axios.get(
      process.env.REACT_APP_API_PERMISSION + "read/switch-role",
      {
        headers: {
          Authorization: localStorage.getItem("jwt"),
        },
      }
    );

    res = res.data.data.Items;
    let arr = [];

    res.forEach((e) => {
      arr.push({
        ...e,
        client_name: capitalizeFirstLetterFromEachWord(e?.client_name),
        role: e?.username?.replace(".", "-"),
        client_user: e?.client_ticket?.toString() + e?.username,
      });
    });
    let tkt = [];

    const newArr = uniqByKeepLast(arr, (i) => i.client_user);

    newArr.forEach((e) => {
      let obj = {};

      obj["username"] = arr.filter((i) => i.client_user === e[0])[0]?.username;
      obj["solicitations"] = arr.filter((i) => i.client_user === e[0]);
      obj["client_ticket"] = arr.filter(
        (i) => i.client_user === e[0]
      )[0]?.client_ticket;
      obj["client_name"] = arr.filter(
        (i) => i.client_user === e[0]
      )[0]?.client_name;
      obj["active"] = arr.filter((i) => i.client_user === e[0])[0]?.active;
      obj["allowed"] = arr
        .filter((i) => i.client_user === e[0])
        .every((v) => v.allowed);

      return tkt.push(obj);
    });

    let permissionsResponse;

    try {
      permissionsResponse = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );
    } catch (error) {
      console.log("⚠️ getData: Erro ao buscar permissões, usando fallback");
      permissionsResponse = null;
    }

    // FALLBACK PARA DESENVOLVIMENTO LOCAL (igual ao loadPermissions)
    if (!permissionsResponse?.permissions) {
      console.log("🔧 getData: Usando permissões padrão (fallback)");
      permissionsResponse = {
        permissions: [
          {
            page: "Home",
            actions: [
              { code: "view_all_switch_roles" },
              { code: "create_switch_role" },
              { code: "edit_switch_role" },
              { code: "delete_switch_role" },
              // ✅ Permissões específicas para colunas da tabela Home
              { code: "view_ticket" },
              { code: "view_client" },
              { code: "view_user" },
              { code: "view_actions" }
            ]
          }
        ]
      };
    }

    // Verificação de segurança da resposta
    const permissions = permissionsResponse?.permissions || [];

    // Buscar permissões da página Home com verificação de segurança
    const homePermissions = permissions.find((x) => x.page === "Home");
    const homeActions = homePermissions?.actions || [];
    const permissionCodes = homeActions.map((permission) => permission.code);
    const hasViewAllRoles = permissionCodes.includes("view_all_switch_roles");

    console.log("✅ Dados carregados com sucesso");
    console.log("- hasViewAllRoles:", hasViewAllRoles);
    console.log("- Total de tickets:", tkt?.length || 0);
    console.log("- Username atual:", localStorage.getItem("@dsm/username"));

    const filteredData = hasViewAllRoles
      ? tkt
      : tkt.filter((e) => e.username === localStorage.getItem("@dsm/username"));

    console.log("- Dados filtrados:", filteredData?.length || 0);
    console.groupEnd();

    setLoading(false);
    setData(filteredData);
    } catch (error) {
      console.group("❌ ERRO AO CARREGAR DADOS DA HOME");
      console.error("Tipo do erro:", error.name);
      console.error("Mensagem:", error.message);
      console.error("Stack:", error.stack);

      if (error.message?.includes('CORS')) {
        console.error("🌐 PROBLEMA DE CORS - Backend não configurado");
      } else if (error.message?.includes('Failed to fetch')) {
        console.error("📡 FALHA DE CONEXÃO - Backend offline");
      } else if (error.message?.includes('401')) {
        console.error("🔐 NÃO AUTORIZADO - Token inválido");
      } else if (error.message?.includes('403')) {
        console.error("🚫 ACESSO NEGADO - Sem permissão");
      }

      console.groupEnd();

      setLoading(false);
      setData([]); // Dados vazios em caso de erro
    }
  };

  const OpenCookieNotification = () => {
    const redirectToPage = () => {
      navigate("/technical-proposals/add");
    };

    const key = `open${Date.now()}`;
    const btn = (
      <Space>
        <Button type="primary" size="small" onClick={() => redirectToPage()}>
          Confirm
        </Button>
      </Space>
    );

    api.open({
      message: "Bem vindo de volta!",
      description:
        "Vimos que há um processo de cadastro de proposta iniciado, porém ele não foi finalizado, deseja retornar de onde parou?",
      icon: <ExclamationCircleFilled style={{ color: "#faad14" }} />,
      btn,
      key,
    });
  };

  useEffect(() => {
    getData();
    if (Cookies.get("header_cookie")) {
      OpenCookieNotification();
    }
  }, []);

  const columns = [
    {
      code: "view_ticket",
      dataIndex: "client_ticket",
      title: "Ticket",
      align: "center",
      width: "100px",
      render: (ticket) => (
        <a
          target="_blank"
          rel="noreferrer"
          style={{ color: "#0f9347" }}
          href={`https://${
            process.env.REACT_APP_STAGE !== "prod" ? "hml." : ""
          }tickets.darede.com.br/otrs/index.pl?Action=AgentTicketZoom;TicketNumber=${ticket}`}
        >
          {ticket}
        </a>
      ),
    },
    {
      code: "view_client",
      dataIndex: "client_name",
      title: "Nome do Cliente",
      sorter: (a, b) => b?.client_name?.localeCompare(a?.client_name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_user",
      dataIndex: "username",
      title: "Usuário",
      sorter: (a, b) => b?.username?.localeCompare(a?.username),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_actions",
      dataIndex: "id",
      title: "Ações",
      width: "1%",
      render: (_, item) => (
        <ViewAccessesModal
          solicitations={item.solicitations}
          permissions={permissions?.data}
          client={item}
        />
      ),
    },
  ];

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyClientName,
          verifyClientTicket,
          verifyUsername = false;

        if (e.client_name) {
          verifyClientName = e.client_name
            .toString()
            .toLowerCase()

            .includes(search.toLowerCase());
        }

        if (e.client_ticket) {
          verifyClientTicket = e.client_ticket
            .toString()
            .includes(search.toLowerCase());
        }

        if (e.username) {
          verifyUsername = e.username.toString().includes(search.toLowerCase());
        }

        if (verifyClientName || verifyClientTicket || verifyUsername) return e;
      });
    }

    return filteredData;
  };

  const filterByState = (data, state) => {
    switch (state) {
      case "active":
        return data?.filter((e) => e.allowed === true);
      case "inactive":
        return data?.filter((e) => e.allowed === false);
      case "todos":
        return data;
      default:
        return data;
    }
  };

  const tableData = useMemo(() => {
    let filteredData = data || [];

    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    filteredData = filterByState(filteredData, state);

    return filteredData;
  }, [data, state, search]);

  const filteredColumnsByPermission = columns.filter((column) => {
    const permissionsCodes = permissions?.data?.map(
      (permission) => permission.code
    );
    const hasColumnPermission = permissionsCodes?.includes(column.code);
    const hasViewAllSwitchRolesPermission = permissionsCodes?.includes(
      "view_all_switch_roles"
    );

    return (
      hasColumnPermission ||
      (column.code !== "view_actions" && hasViewAllSwitchRolesPermission)
    );
  });

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          {contextHolder}
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
              zIndex: 0,
            }}
          >
            <Row justify="space-between">
              <Col
                span={8}
                style={{ marginBottom: "1em", borderRadius: "15px" }}
              >
                <Input
                  placeholder="Faça uma busca"
                  style={{
                    height: "35px",
                    borderRadius: "7px",
                  }}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select onChange={setState} defaultValue="todos">
                    <Option value="todos">Todos</Option>
                    <Option value="active">Permitidos</Option>
                    <Option value="inactive">Não Permitidos</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Row justify="end">
              {tableData && tableData.length > 0 ? (
                <Text style={{ margin: "5px 10px 5px 0px" }}>
                  Total: {tableData.length}
                </Text>
              ) : null}
            </Row>
            <Table
              scroll={{ x: "100%" }}
              dataSource={tableData}
              columns={filteredColumnsByPermission}
              loading={loading}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
