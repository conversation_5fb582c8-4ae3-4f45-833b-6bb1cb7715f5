/* ✅ Estilos customizados para o SideMenu DSM */

/* ✅ Variáveis locais para consistência */
:root {
  --dsm-menu-primary: #00B050;
  --dsm-menu-primary-light: rgba(0, 176, 80, 0.2);
  --dsm-menu-white: #ffffff;
  --dsm-menu-bg: rgb(53, 53, 53);
}

/* ✅ Item de menu selecionado */
.ant-menu-dark .ant-menu-item-selected,
.ant-menu-dark .ant-menu-item-selected:hover,
.ant-menu-dark .ant-menu-item-selected:focus {
  background-color: var(--dsm-menu-primary) !important;
  color: var(--dsm-menu-white) !important;
}

.ant-menu-dark .ant-menu-item-selected a,
.ant-menu-dark .ant-menu-item-selected:hover a,
.ant-menu-dark .ant-menu-item-selected:focus a {
  color: var(--dsm-menu-white) !important;
}

/* ✅ Item de menu em hover */
.ant-menu-dark .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: var(--dsm-menu-primary-light) !important;
  color: var(--dsm-menu-primary) !important;
}

.ant-menu-dark .ant-menu-item:hover:not(.ant-menu-item-selected) a {
  color: var(--dsm-menu-primary) !important;
}

/* ✅ Submenu selecionado */
.ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title,
.ant-menu-dark .ant-menu-submenu-selected:hover > .ant-menu-submenu-title {
  background-color: var(--dsm-menu-primary) !important;
  color: var(--dsm-menu-white) !important;
}

/* ✅ Submenu em hover */
.ant-menu-dark .ant-menu-submenu:hover:not(.ant-menu-submenu-selected) > .ant-menu-submenu-title {
  background-color: var(--dsm-menu-primary-light) !important;
  color: var(--dsm-menu-primary) !important;
}

/* ✅ Itens dentro de submenu selecionados */
.ant-menu-dark .ant-menu-submenu .ant-menu-item-selected,
.ant-menu-dark .ant-menu-submenu .ant-menu-item-selected:hover {
  background-color: var(--dsm-menu-primary) !important;
  color: var(--dsm-menu-white) !important;
}

.ant-menu-dark .ant-menu-submenu .ant-menu-item-selected a,
.ant-menu-dark .ant-menu-submenu .ant-menu-item-selected:hover a {
  color: var(--dsm-menu-white) !important;
}

/* ✅ Links em geral */
.ant-menu-dark a {
  color: inherit !important;
  transition: color 0.3s ease;
}

.ant-menu-dark a:hover {
  color: var(--dsm-menu-primary) !important;
}

/* ✅ Indicador de item ativo (barra lateral) */
.ant-menu-dark .ant-menu-item-selected::after {
  border-right-color: var(--dsm-menu-primary) !important;
  border-right-width: 3px !important;
}

/* ✅ Setas do submenu */
.ant-menu-dark .ant-menu-submenu-selected .ant-menu-submenu-arrow::before,
.ant-menu-dark .ant-menu-submenu-selected .ant-menu-submenu-arrow::after {
  background: var(--dsm-menu-white) !important;
}

/* ✅ Grupos de menu (títulos) */
.ant-menu-dark .ant-menu-item-group-title {
  color: rgba(255, 255, 255, 0.67) !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 8px 24px 8px 24px !important;
}

/* ✅ Ícones dos itens de menu */
.ant-menu-dark .ant-menu-item .anticon,
.ant-menu-dark .ant-menu-submenu-title .anticon {
  color: inherit !important;
  transition: color 0.3s ease;
}

/* ✅ Estados de foco para acessibilidade */
.ant-menu-dark .ant-menu-item:focus,
.ant-menu-dark .ant-menu-submenu-title:focus {
  background-color: var(--dsm-menu-primary-light) !important;
  color: var(--dsm-menu-primary) !important;
}

/* ✅ Submenu aberto */
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--dsm-menu-primary) !important;
}

/* ✅ Transições suaves */
.ant-menu-dark .ant-menu-item,
.ant-menu-dark .ant-menu-submenu-title {
  transition: all 0.3s ease !important;
}

/* ✅ Correção para itens inline (submenu aberto) */
.ant-menu-dark.ant-menu-inline .ant-menu-item-selected {
  background-color: var(--dsm-menu-primary) !important;
}

.ant-menu-dark.ant-menu-inline .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: var(--dsm-menu-primary-light) !important;
}
