/**
 * Componente de Inicialização da API
 * Configura dinamicamente as URLs da API no início da aplicação
 */

import React, { useEffect, useState } from 'react';
import { Spin, Alert, Button } from 'antd';
import { LoadingOutlined, ReloadOutlined } from '@ant-design/icons';
import { initializeApiConfig, dynamicApiConfig } from '../../services/dynamicApiConfig';

const ApiInitializer = ({ children }) => {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  const initializeApi = async () => {
    try {
      setIsInitializing(true);
      setInitError(null);
      
      console.log('🚀 Inicializando configuração dinâmica da API...');
      
      const success = await initializeApiConfig();
      
      if (success) {
        console.log('✅ API configurada com sucesso');
        setIsInitializing(false);
      } else {
        throw new Error('Falha na inicialização da API');
      }
    } catch (error) {
      console.error('❌ Erro na inicialização da API:', error);
      setInitError(error.message);
      setIsInitializing(false);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    initializeApi();
  };

  useEffect(() => {
    initializeApi();
  }, []);

  // Se ainda está inicializando, mostrar loading
  if (isInitializing) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f0f2f5'
      }}>
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} 
          size="large" 
        />
        <div style={{ marginTop: 24, textAlign: 'center' }}>
          <h3>Configurando API...</h3>
          <p>Obtendo configuração dinâmica do servidor</p>
          {retryCount > 0 && (
            <p style={{ color: '#666' }}>
              Tentativa {retryCount + 1}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Se houve erro na inicialização, mostrar opções
  if (initError) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f0f2f5',
        padding: 24
      }}>
        <Alert
          message="Erro na Configuração da API"
          description={
            <div>
              <p>Não foi possível obter a configuração dinâmica da API.</p>
              <p><strong>Erro:</strong> {initError}</p>
              <p>A aplicação continuará funcionando com configuração de fallback.</p>
            </div>
          }
          type="warning"
          showIcon
          style={{ maxWidth: 600, marginBottom: 24 }}
        />
        
        <div style={{ display: 'flex', gap: 16 }}>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={handleRetry}
          >
            Tentar Novamente
          </Button>
          
          <Button 
            onClick={() => {
              setInitError(null);
              setIsInitializing(false);
            }}
          >
            Continuar com Fallback
          </Button>
        </div>
        
        {retryCount >= 3 && (
          <Alert
            message="Múltiplas tentativas falharam"
            description="Recomendamos continuar com a configuração de fallback ou verificar a conectividade."
            type="info"
            style={{ marginTop: 16, maxWidth: 600 }}
          />
        )}
      </div>
    );
  }

  return children;
};

export default ApiInitializer;
