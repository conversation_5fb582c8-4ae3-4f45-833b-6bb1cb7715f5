import axios from 'axios'
import { authService } from '../services/authService'
// import { httpOnlyAuthService } from '../services/httpOnlyAuthService' // Removido - não existe mais
import { config } from './config'
import { logger } from './logger'

export const URLS = {
    DSM: process.env.REACT_APP_API_PERMISSION,
    PROPOSALS: process.env.REACT_APP_API_PROPOSALS,
    OTRS: process.env.REACT_APP_API_OTRS_BASE_URL,
    REPORTS: process.env.REACT_APP_API_REPORTS_URL
}

// Instância para DSM API
export const apiDsm = axios.create({
    baseURL: URLS.DSM,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

// Interceptor para adicionar token automaticamente
if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.request.use(
        (config) => {
            // Tentar obter token do httpOnlyAuthService primeiro
            let token = null;

        try {
            // Sistema original: usar token do localStorage
            token = authService.getToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
                logger.debug('Usando token do localStorage para DSM API');
            }
        } catch (error) {
            logger.warn('Erro ao configurar autenticação para DSM API:', error);
        }

        logger.debug('DSM API Request:', {
            method: config.method?.toUpperCase(),
            url: config.url,
            hasAuth: !!token || config.withCredentials
        });

        return config;
    },
    (error) => {
        logger.error('Erro no interceptor de request da DSM API:', error);
        return Promise.reject(error);
    }
);
}

// Interceptor de response para logging
if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.response.use(
    (response) => {
        logger.debug('DSM API Response:', {
            status: response.status,
            url: response.config?.url
        });
        return response;
    },
    (error) => {
        logger.error('DSM API Error:', {
            status: error.response?.status,
            url: error.config?.url,
            message: error.message
        });
        return Promise.reject(error);
    }
);
}

export const apiProposals = axios.create({
    baseURL: URLS.PROPOSALS
})

export const apiOTRS = axios.create({
    baseURL: URLS.OTRS,
    headers: {
        Authorization: config.OTRS.AUTH
    }
})

export const apiReports = axios.create({
    baseURL: URLS.REPORTS
})

export const getHeader = () => {
    return authService.getAuthHeaders();
}