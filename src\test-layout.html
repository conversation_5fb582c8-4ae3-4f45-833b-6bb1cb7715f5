<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Layout DSM - Comparação Visual</title>
    <link href="https://fonts.googleapis.com/css2?family=Libre+Franklin:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Libre Franklin', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            font-size: 14px;
            line-height: 1.5714285714285714;
            color: #000000d9;
        }
        
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .version-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .version-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #00B050;
            border-bottom: 2px solid #00B050;
            padding-bottom: 10px;
        }
        
        /* Simulação de componentes Antd */
        .mock-button {
            display: inline-block;
            padding: 4px 15px;
            height: 32px;
            line-height: 24px;
            font-size: 14px;
            font-weight: 400;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            margin: 5px;
        }
        
        .mock-button-primary {
            background-color: #00B050;
            border-color: #00B050;
            color: white;
        }
        
        .mock-input {
            display: block;
            width: 100%;
            height: 32px;
            padding: 4px 11px;
            font-size: 14px;
            line-height: 1.5714285714285714;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin: 5px 0;
        }
        
        .mock-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .mock-table th {
            background-color: #fafafa;
            padding: 16px;
            font-weight: 600;
            font-size: 14px;
            border: 1px solid #f0f0f0;
            text-align: left;
        }
        
        .mock-table td {
            padding: 16px;
            font-size: 14px;
            font-weight: 400;
            border: 1px solid #f0f0f0;
        }
        
        .mock-card {
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .mock-card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .mock-card-body {
            padding: 24px;
        }
        
        .mock-tag {
            display: inline-block;
            padding: 0 7px;
            height: 22px;
            line-height: 20px;
            font-size: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin: 2px;
        }
        
        .mock-tag-green {
            background-color: rgba(0, 176, 80, 0.2);
            border-color: #00B050;
            color: #00B050;
        }
        
        .mock-pagination {
            text-align: center;
            margin: 20px 0;
        }
        
        .mock-pagination-item {
            display: inline-block;
            min-width: 32px;
            height: 32px;
            line-height: 30px;
            text-align: center;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 0 4px;
            font-size: 14px;
            font-weight: 400;
        }
        
        .mock-pagination-item-active {
            border-color: #00B050;
            color: #00B050;
        }
        
        /* Problemas identificados */
        .problem-original {
            background-color: #fff2f0;
            border-left: 4px solid #ff4d4f;
            padding: 10px;
            margin: 10px 0;
        }
        
        .problem-current {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 10px;
            margin: 10px 0;
        }
        
        .issue-list {
            list-style: none;
            padding: 0;
        }
        
        .issue-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .issue-list li:before {
            content: "⚠️ ";
            margin-right: 5px;
        }
        
        .fix-list li:before {
            content: "✅ ";
            margin-right: 5px;
        }
        
        h1 { color: #00B050; font-weight: 600; }
        h2 { color: #404040; font-weight: 600; }
        h3 { color: #404040; font-weight: 500; }
    </style>
</head>
<body>
    <h1>🔍 Teste de Layout DSM - Análise Visual Comparativa</h1>
    
    <div class="comparison-container">
        <div class="version-panel">
            <div class="version-title">📋 PROBLEMAS IDENTIFICADOS</div>
            
            <h3>Diferenças Visuais Reportadas:</h3>
            <ul class="issue-list">
                <li>Componentes parecem maiores que o original</li>
                <li>Texto em negrito onde não deveria ter</li>
                <li>Espaçamentos diferentes</li>
                <li>Densidade visual alterada</li>
                <li>Possível mudança de fonte ou peso</li>
            </ul>
            
            <div class="problem-original">
                <strong>Possíveis Causas:</strong><br>
                • Antd v5 tem padrões diferentes do v4<br>
                • ConfigProvider alterando configurações<br>
                • CSS conflitante<br>
                • Fonte carregada incorretamente<br>
                • Webpack LESS com configurações diferentes
            </div>
        </div>
        
        <div class="version-panel">
            <div class="version-title">🔧 CORREÇÕES IMPLEMENTADAS</div>
            
            <h3>Soluções Aplicadas:</h3>
            <ul class="issue-list fix-list">
                <li>ConfigProvider simplificado (apenas cores)</li>
                <li>CSS de normalização criado</li>
                <li>Font-weight corrigido para 400 (normal)</li>
                <li>Fonte Libre Franklin com todos os pesos</li>
                <li>Espaçamentos padronizados</li>
                <li>Tamanhos normalizados para 14px</li>
            </ul>
            
            <div class="problem-current">
                <strong>Resultado Esperado:</strong><br>
                • Visual idêntico ao original<br>
                • Sem negritos desnecessários<br>
                • Tamanhos consistentes<br>
                • Espaçamentos corretos<br>
                • Cores DSM mantidas
            </div>
        </div>
    </div>
    
    <h2>🎯 Componentes de Teste</h2>
    
    <div class="comparison-container">
        <div class="version-panel">
            <div class="version-title">Botões</div>
            <button class="mock-button">Botão Normal</button>
            <button class="mock-button-primary">Botão Primário</button>
            
            <div class="version-title" style="margin-top: 20px;">Input</div>
            <input class="mock-input" placeholder="Campo de texto" value="Texto de exemplo">
            
            <div class="version-title" style="margin-top: 20px;">Tags</div>
            <span class="mock-tag">Tag Normal</span>
            <span class="mock-tag mock-tag-green">Tag Verde DSM</span>
        </div>
        
        <div class="version-panel">
            <div class="version-title">Tabela</div>
            <table class="mock-table">
                <thead>
                    <tr>
                        <th>Coluna 1</th>
                        <th>Coluna 2</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Dados normais</td>
                        <td>Sem negrito</td>
                        <td><span class="mock-tag mock-tag-green">Ativo</span></td>
                    </tr>
                    <tr>
                        <td>Mais dados</td>
                        <td>Peso 400</td>
                        <td><span class="mock-tag">Pendente</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="comparison-container">
        <div class="version-panel">
            <div class="version-title">Card</div>
            <div class="mock-card">
                <div class="mock-card-header">Título do Card</div>
                <div class="mock-card-body">
                    <p>Conteúdo do card com texto normal, sem negrito desnecessário. O peso da fonte deve ser 400 (normal) para texto comum.</p>
                    <p><strong>Apenas este texto deve estar em negrito (peso 600).</strong></p>
                </div>
            </div>
        </div>
        
        <div class="version-panel">
            <div class="version-title">Paginação</div>
            <div class="mock-pagination">
                <span class="mock-pagination-item">1</span>
                <span class="mock-pagination-item mock-pagination-item-active">2</span>
                <span class="mock-pagination-item">3</span>
                <span class="mock-pagination-item">4</span>
                <span class="mock-pagination-item">5</span>
            </div>
            
            <h3>Especificações:</h3>
            <ul>
                <li><strong>Fonte:</strong> Libre Franklin</li>
                <li><strong>Tamanho:</strong> 14px</li>
                <li><strong>Peso normal:</strong> 400</li>
                <li><strong>Peso negrito:</strong> 600</li>
                <li><strong>Altura botão:</strong> 32px</li>
                <li><strong>Altura input:</strong> 32px</li>
                <li><strong>Border radius:</strong> 6px</li>
            </ul>
        </div>
    </div>
    
    <footer style="text-align: center; margin-top: 40px; color: #666; border-top: 1px solid #f0f0f0; padding-top: 20px;">
        <p>🔧 <strong>Instruções de Teste:</strong></p>
        <p>1. Compare este layout com o frontend original</p>
        <p>2. Verifique se os tamanhos estão similares</p>
        <p>3. Confirme que não há negritos desnecessários</p>
        <p>4. Teste a responsividade</p>
        <p>5. Valide as cores DSM (#00B050)</p>
    </footer>
</body>
</html>
