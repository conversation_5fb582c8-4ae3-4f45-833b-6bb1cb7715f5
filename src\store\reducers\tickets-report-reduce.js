import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";
import { serializeDate, deserializeDate } from "../../utils/dateSerializer";

const REDUCER_NAME = "ticketsReport";

export const allOption = { value: "Todos", label: "Todos" };

export const INITIAL_STATE = {
  customers: [],
  customerSelected: null,
  contracts: [allOption],
  contractSelected: allOption,
  tickets: [],
  search: "",
  filteredTickets: [],
  month: serializeDate(moment()), // Serialize moment object
};

const ticketReportSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setTicketReportReduce(state, action) {
      const { field, value } = action.payload;

      // Handle moment objects by serializing them
      if (field === 'month' && moment.isMoment(value)) {
        state[field] = serializeDate(value);
      } else {
        state[field] = value;
      }
    },
  },
});

export const { setTicketReportReduce } = ticketReportSlice.actions;

// Selectors to handle serialized dates
export const selectTicketReportMonth = (state) => {
  const monthValue = state.ticketReport.month;
  return typeof monthValue === 'string' ? deserializeDate(monthValue) : moment();
};

export default ticketReportSlice.reducer;
